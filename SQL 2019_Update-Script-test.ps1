﻿# SQL Server 2019 Update Script - Enhanced Version
# This script downloads and installs SQL Server 2019 CU32 with improved error handling and logging

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER", # Default instance or specify named instance

    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst,

    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\temp\SQLServer2019Update.log"
)

# Define variables
$DownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-*********-x64.exe"
$DownloadPath = "C:\temp\SQLServer2019-CU32-x64.exe"

# Function to write log entries
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    Write-Host $logEntry

    # Ensure log directory exists
    $logDir = Split-Path $LogPath -Parent
    if (!(Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }

    Add-Content -Path $LogPath -Value $logEntry -Force
}

# Function to check if user has admin rights
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to get service names based on instance
function Get-ServiceNames {
    param([string]$Instance)

    if ($Instance -eq "MSSQLSERVER") {
        return @{
            Engine = "MSSQLSERVER"
            Agent = "SQLSERVERAGENT"
        }
    } else {
        return @{
            Engine = "MSSQL`$$Instance"
            Agent = "SQLAgent`$$Instance"
        }
    }
}

Write-Log "Starting SQL Server 2019 CU32 Update Process"

# Check admin rights
if (!(Test-AdminRights)) {
    Write-Log "This script requires administrator privileges" -Level "ERROR"
    exit 1
}

# Create temp directory if it doesn't exist
if (!(Test-Path "C:\temp")) {
    New-Item -Path "C:\temp" -ItemType Directory -Force | Out-Null
    Write-Log "Created temp directory: C:\temp"
}

## Download the CU Installer
Write-Log "Downloading SQL Server 2019 CU32 from Microsoft Download Center..."
try {
    # Check if file already exists
    if (Test-Path $DownloadPath) {
        Write-Log "Update file already exists at: $DownloadPath"
    } else {
        Invoke-WebRequest -Uri $DownloadUrl -OutFile $DownloadPath -UseBasicParsing
        Write-Log "Download complete. File saved to: $DownloadPath"
    }

    # Verify file was downloaded
    if (!(Test-Path $DownloadPath)) {
        throw "Downloaded file not found at expected location"
    }
} catch {
    Write-Log "Failed to download the file: $($_.Exception.Message)" -Level "ERROR"
    exit 1
}

# Get service names for the instance
$serviceNames = Get-ServiceNames -Instance $InstanceName
Write-Log "Target instance: $InstanceName"
Write-Log "Engine service: $($serviceNames.Engine)"
Write-Log "Agent service: $($serviceNames.Agent)"

# Function to backup databases (optional)
function Backup-SQLDatabases {
    param([string]$Instance)

    if (!$BackupFirst) {
        Write-Log "Backup not requested, skipping database backup"
        return $true
    }

    try {
        Write-Log "Starting database backup for instance: $Instance"

        $serverName = if ($Instance -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$Instance" }
        $backupPath = "C:\temp\SQLBackups\$Instance"

        if (!(Test-Path $backupPath)) {
            New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        }

        # Use SQLCMD to backup system databases
        $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1

        if ($sqlcmdPath) {
            $backupScript = @"
BACKUP DATABASE [master] TO DISK = '$backupPath\master_backup.bak' WITH INIT;
BACKUP DATABASE [model] TO DISK = '$backupPath\model_backup.bak' WITH INIT;
BACKUP DATABASE [msdb] TO DISK = '$backupPath\msdb_backup.bak' WITH INIT;
"@

            $backupScript | Out-File -FilePath "$backupPath\backup_script.sql" -Force

            & $sqlcmdPath.FullName -S $serverName -E -i "$backupPath\backup_script.sql"
            Write-Log "Database backup completed for instance: $Instance"
            return $true
        }
        else {
            Write-Log "SQLCMD not found, skipping backup" -Level "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Error during backup: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Function to check if services need to be stopped for update
function Test-ServiceStopRequired {
    param([string]$Instance)

    Write-Log "Checking if SQL services need to be stopped for update..."

    # For SQL Server cumulative updates, services typically need to be stopped
    # This is required for file replacement during the update process
    $serviceNames = Get-ServiceNames -Instance $Instance

    $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
    if ($engineService -and $engineService.Status -eq "Running") {
        Write-Log "SQL Server Engine is running - services will need to be stopped for update"
        return $true
    } else {
        Write-Log "SQL Server Engine is not running - no need to stop services"
        return $false
    }
}

# Function to safely stop SQL Server services
function Stop-SQLServerServices {
    param([string]$Instance)

    $serviceNames = Get-ServiceNames -Instance $Instance
    $servicesStopped = @()

    try {
        Write-Log "Stopping SQL Server services for update..."

        # Stop SQL Server Agent first (if running)
        $agentService = Get-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue
        if ($agentService -and $agentService.Status -eq "Running") {
            Write-Log "Stopping SQL Server Agent: $($serviceNames.Agent)"
            Stop-Service -Name $serviceNames.Agent -Force
            Wait-Service -Name $serviceNames.Agent -Status Stopped -Timeout 60
            Write-Log "SQL Server Agent stopped successfully"
            $servicesStopped += $serviceNames.Agent
        } else {
            Write-Log "SQL Server Agent is not running or not found"
        }

        # Stop SQL Server Engine (if running)
        $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
        if ($engineService -and $engineService.Status -eq "Running") {
            Write-Log "Stopping SQL Server Engine: $($serviceNames.Engine)"
            Stop-Service -Name $serviceNames.Engine -Force
            Wait-Service -Name $serviceNames.Engine -Status Stopped -Timeout 120
            Write-Log "SQL Server Engine stopped successfully"
            $servicesStopped += $serviceNames.Engine
        } else {
            Write-Log "SQL Server Engine is not running or not found"
        }

        # Wait a moment for services to fully stop
        Start-Sleep -Seconds 5

        return $servicesStopped

    } catch {
        Write-Log "Error stopping SQL Server services: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# Backup databases if requested (only if services are running)
if ($BackupFirst) {
    $needsServiceStop = Test-ServiceStopRequired -Instance $InstanceName
    if ($needsServiceStop) {
        Write-Log "Performing backup before stopping services..."
        if (!(Backup-SQLDatabases -Instance $InstanceName)) {
            Write-Log "Backup failed, but continuing with update..." -Level "WARNING"
        }
    } else {
        Write-Log "SQL Server services not running - skipping backup"
    }
}

## Stop SQL Server Services (only if required)
$stoppedServices = @()
$needsServiceStop = Test-ServiceStopRequired -Instance $InstanceName

if ($needsServiceStop) {
    try {
        $stoppedServices = Stop-SQLServerServices -Instance $InstanceName
        Write-Log "Services stopped successfully. Stopped services: $($stoppedServices -join ', ')"
    } catch {
        Write-Log "Failed to stop SQL Server services: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
} else {
    Write-Log "SQL Server services are not running - proceeding with update without stopping services"
}

# Function to check if current update requires service restart
function Test-UpdateRequiresServiceRestart {
    Write-Log "Checking if this update type requires SQL service restart..."

    # For SQL Server Cumulative Updates (CU), services typically need to be stopped
    # This is because CU updates replace core SQL Server binaries
    # Security updates and hotfixes may also require service restart

    # Since this is a CU32 update, it will require service restart
    Write-Log "SQL Server Cumulative Update detected - service restart will be required"
    return $true
}

## Install the CU
Write-Log "Starting installation of SQL Server 2019 CU32..."

# Check if this update requires service restart
$requiresServiceRestart = Test-UpdateRequiresServiceRestart
Write-Log "Service restart required for this update: $requiresServiceRestart"

try {
    # Enhanced installation arguments for better compatibility
    $installArgs = @(
        "/quiet",                           # Silent installation
        "/IAcceptSQLServerLicenseTerms",   # Accept license terms
        "/Action=Patch"                    # Specify patch action
    )

    # Add instance-specific arguments
    if ($InstanceName -eq "MSSQLSERVER") {
        $installArgs += "/AllInstances"     # Apply to all instances for default
    } else {
        $installArgs += "/InstanceName=$InstanceName"  # Apply to specific named instance
    }

    # Add additional safety parameters
    $installArgs += "/IACCEPTSQLSERVERLICENSETERMS"  # Ensure license acceptance
    $installArgs += "/SUPPRESSPRIVACYSTATEMENTNOTICE=True"  # Suppress privacy notice

    Write-Log "Installation arguments: $($installArgs -join ' ')"
    Write-Log "Target instance: $InstanceName"
    Write-Log "Services stopped for update: $($stoppedServices.Count -gt 0)"

    $process = Start-Process -FilePath $DownloadPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow

    Write-Log "Installation process completed with exit code: $($process.ExitCode)"

    # Handle different exit codes
    switch ($process.ExitCode) {
        0 {
            Write-Log "Installation completed successfully"
        }
        3010 {
            Write-Log "Installation completed successfully - System reboot required" -Level "WARNING"
        }
        1641 {
            Write-Log "Installation completed successfully - Installer initiated reboot" -Level "WARNING"
        }
        default {
            Write-Log "Installation failed with exit code: $($process.ExitCode)" -Level "ERROR"

            # Common exit codes and their meanings
            $exitCodeMeanings = @{
                1603 = "Fatal error during installation"
                1619 = "Installation package could not be opened"
                1620 = "Installation package could not be opened (corrupt)"
                1633 = "Installation package is not supported on this platform"
                1638 = "Another version of this product is already installed"
            }

            if ($exitCodeMeanings.ContainsKey($process.ExitCode)) {
                Write-Log "Exit code meaning: $($exitCodeMeanings[$process.ExitCode])" -Level "ERROR"
            }

            throw "Installation failed with exit code: $($process.ExitCode)"
        }
    }

} catch {
    Write-Log "An error occurred during installation: $($_.Exception.Message)" -Level "ERROR"

    # If services were stopped, try to restart them after installation failure
    if ($stoppedServices.Count -gt 0) {
        Write-Log "Attempting to restart services after installation failure..." -Level "WARNING"
        try {
            Start-SQLServerServices -Instance $InstanceName -ServicesToStart $stoppedServices
            Write-Log "Services restarted after installation failure"
        } catch {
            Write-Log "Failed to restart services after installation failure - manual intervention required" -Level "ERROR"
        }
    }
    exit 1
}

# Function to start SQL Server services (only those that were stopped)
function Start-SQLServerServices {
    param(
        [string]$Instance,
        [array]$ServicesToStart
    )

    if ($ServicesToStart.Count -eq 0) {
        Write-Log "No services to start - they were not stopped by this script"
        return
    }

    $serviceNames = Get-ServiceNames -Instance $Instance

    try {
        Write-Log "Starting SQL Server services that were stopped..."

        # Start SQL Server Engine first (if it was stopped)
        if ($ServicesToStart -contains $serviceNames.Engine) {
            Write-Log "Starting SQL Server Engine: $($serviceNames.Engine)"
            Start-Service -Name $serviceNames.Engine
            Wait-Service -Name $serviceNames.Engine -Status Running -Timeout 120
            Write-Log "SQL Server Engine started successfully"

            # Wait for engine to fully initialize
            Start-Sleep -Seconds 15
        }

        # Start SQL Server Agent (if it was stopped)
        if ($ServicesToStart -contains $serviceNames.Agent) {
            Write-Log "Starting SQL Server Agent: $($serviceNames.Agent)"
            $agentService = Get-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue
            if ($agentService) {
                Start-Service -Name $serviceNames.Agent
                Wait-Service -Name $serviceNames.Agent -Status Running -Timeout 60
                Write-Log "SQL Server Agent started successfully"
            } else {
                Write-Log "SQL Server Agent service not found" -Level "WARNING"
            }
        }

    } catch {
        Write-Log "Error starting SQL Server services: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

## Restart SQL Server Services (only those that were stopped)
if ($stoppedServices.Count -gt 0) {
    Write-Log "Restarting SQL Server services that were stopped for the update..."
    try {
        Start-SQLServerServices -Instance $InstanceName -ServicesToStart $stoppedServices
        Write-Log "All stopped services have been restarted successfully"
    } catch {
        Write-Log "Failed to restart SQL Server services: $($_.Exception.Message)" -Level "ERROR"

        # Attempt emergency restart of critical services
        Write-Log "Attempting emergency restart of SQL Server Engine..." -Level "WARNING"
        try {
            $serviceNames = Get-ServiceNames -Instance $InstanceName
            Start-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
            Write-Log "Emergency restart of SQL Server Engine completed"
        } catch {
            Write-Log "Emergency restart failed - manual intervention may be required" -Level "ERROR"
        }
        exit 1
    }
} else {
    Write-Log "No services were stopped by this script - no restart required"
}

## Post-installation verification
Write-Log "Performing post-installation verification..."
try {
    # Check service status
    $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
    if ($engineService -and $engineService.Status -eq "Running") {
        Write-Log "SQL Server Engine is running successfully"
    } else {
        Write-Log "SQL Server Engine is not running properly" -Level "WARNING"
    }

    # Try to get SQL Server version using SQLCMD
    $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($sqlcmdPath) {
        $serverName = if ($InstanceName -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$InstanceName" }
        $versionQuery = "SELECT @@VERSION"

        try {
            $version = & $sqlcmdPath.FullName -S $serverName -E -Q $versionQuery -h -1 2>$null
            if ($version) {
                Write-Log "SQL Server version verification successful"
                Write-Log "Version info: $($version.Trim())"
            }
        } catch {
            Write-Log "Could not verify SQL Server version, but services are running" -Level "WARNING"
        }
    }
} catch {
    Write-Log "Post-installation verification encountered issues: $($_.Exception.Message)" -Level "WARNING"
}

## Cleanup
Write-Log "Performing cleanup..."
try {
    # Cleanup the downloaded installer file
    if (Test-Path $DownloadPath) {
        Remove-Item -Path $DownloadPath -Force
        Write-Log "Installer file deleted successfully"
    }

    Write-Log "SQL Server 2019 CU32 update process completed successfully"

} catch {
    Write-Log "Cleanup failed: $($_.Exception.Message)" -Level "WARNING"
}

Write-Log "Script execution finished. Check log file for details: $LogPath"