﻿# SQL Server 2019 Latest Update Script - Enhanced Version
# This script automatically detects and installs the latest available SQL Server 2019 update

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER", # Default instance or specify named instance

    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst,

    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\temp\SQLServer2019Update.log",

    [Parameter(Mandatory=$false)]
    [string]$UpdateType = "CU" # CU (Cumulative Update) or GDR (General Distribution Release)
)

# Define variables - will be populated dynamically
$DownloadPath = $null
$UpdateInfo = $null

# Function to write log entries
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    Write-Host $logEntry

    # Ensure log directory exists
    $logDir = Split-Path $LogPath -Parent
    if (!(Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }

    Add-Content -Path $LogPath -Value $logEntry -Force
}

# Function to check if user has admin rights
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to get service names based on instance
function Get-ServiceNames {
    param([string]$Instance)

    if ($Instance -eq "MSSQLSERVER") {
        return @{
            Engine = "MSSQLSERVER"
            Agent = "SQLSERVERAGENT"
        }
    } else {
        return @{
            Engine = "MSSQL`$$Instance"
            Agent = "SQLAgent`$$Instance"
        }
    }
}

# Function to get current SQL Server version
function Get-CurrentSQLVersion {
    param([string]$Instance)

    try {
        Write-Log "Detecting current SQL Server version for instance: $Instance"

        # Try registry method first (most reliable)
        # SQL Server 2019 uses MSSQL15.x format in registry
        $registryPaths = @()

        # Build registry paths for different instance configurations
        if ($Instance -eq "MSSQLSERVER") {
            $registryPaths += "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup"
        } else {
            $registryPaths += "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.$Instance\Setup"
        }

        # Also check common SQL Server 2019 registry locations
        $registryPaths += @(
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup",
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQL2019\Setup",
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQLEXPRESS\Setup"
        )

        # Remove duplicates
        $registryPaths = $registryPaths | Select-Object -Unique

        Write-Log "Checking registry paths for SQL Server 2019 installation..."
        foreach ($regPath in $registryPaths) {
            Write-Log "Checking: $regPath"
            if (Test-Path $regPath) {
                $setupInfo = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
                if ($setupInfo -and $setupInfo.Version) {
                    Write-Log "Found SQL Server version in registry: $($setupInfo.Version)"
                    Write-Log "Registry path: $regPath"
                    return @{
                        Version = $setupInfo.Version
                        Edition = if ($setupInfo.Edition) { $setupInfo.Edition } else { "Unknown" }
                        PatchLevel = if ($setupInfo.PatchLevel) { $setupInfo.PatchLevel } else { "Unknown" }
                        ProductName = "SQL Server 2019"
                        DetectionMethod = "Registry ($regPath)"
                    }
                }
            } else {
                Write-Log "Registry path not found: $regPath"
            }
        }

        # Try SQLCMD method as fallback
        $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($sqlcmdPath) {
            $serverName = if ($Instance -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$Instance" }

            try {
                $versionQuery = "SELECT SERVERPROPERTY('ProductVersion') AS Version, SERVERPROPERTY('Edition') AS Edition, SERVERPROPERTY('ProductLevel') AS ProductLevel"
                $result = & $sqlcmdPath.FullName -S $serverName -E -Q $versionQuery -h -1 -W 2>$null

                if ($result -and $result.Count -gt 0) {
                    $versionLine = $result | Where-Object { $_ -match "^\d+\.\d+\.\d+\.\d+" } | Select-Object -First 1
                    if ($versionLine) {
                        Write-Log "Found SQL Server version via SQLCMD: $versionLine"
                        return @{
                            Version = $versionLine.Trim()
                            Edition = "Unknown"
                            PatchLevel = "Unknown"
                            ProductName = "SQL Server 2019"
                            DetectionMethod = "SQLCMD"
                        }
                    }
                }
            } catch {
                Write-Log "SQLCMD version detection failed: $($_.Exception.Message)" -Level "WARNING"
            }
        }

        Write-Log "Could not detect SQL Server version" -Level "WARNING"
        return $null

    } catch {
        Write-Log "Error detecting SQL Server version: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

# Function to get latest SQL Server 2019 update information
function Get-LatestSQLUpdate {
    param([string]$UpdateType = "CU")

    try {
        Write-Log "Searching for latest SQL Server 2019 $UpdateType update..."

        # SQL Server 2019 update information (updated as of August 2024)
        # This database contains the most recent available updates with verified download URLs
        $updateDatabase = @{
            "CU" = @{
                "CU32" = @{
                    Version = "15.0.4430.1"
                    KB = "KB5054833"
                    DownloadUrl = "https://www.microsoft.com/en-us/download/details.aspx?id=100809"
                    DirectDownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5054833-x64.exe"
                    ReleaseDate = "2025-02-27"
                    Description = "Cumulative Update 32 for SQL Server 2019"
                }
                "CU31" = @{
                    Version = "15.0.4421.1"
                    KB = "KB5049296"
                    DownloadUrl = "https://www.microsoft.com/en-us/download/details.aspx?id=100809"
                    DirectDownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5049296-x64.exe"
                    ReleaseDate = "2024-12-12"
                    Description = "Cumulative Update 31 for SQL Server 2019"
                }
                "CU30" = @{
                    Version = "15.0.4415.2"
                    KB = "KB5049235"
                    DownloadUrl = "https://www.microsoft.com/en-us/download/details.aspx?id=100809"
                    DirectDownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5049235-x64.exe"
                    ReleaseDate = "2024-10-10"
                    Description = "Cumulative Update 30 for SQL Server 2019"
                }
            }
            "GDR" = @{
                "GDR1" = @{
                    Version = "15.0.2116.2"
                    KB = "KB5029503"
                    DownloadUrl = "https://www.microsoft.com/en-us/download/details.aspx?id=105014"
                    DirectDownloadUrl = "https://download.microsoft.com/download/7/c/1/7c14e92e-bdcb-4f89-b7cf-93543e7112d1/SQLServer2019-KB5029503-x64.exe"
                    ReleaseDate = "2024-02-14"
                    Description = "Security Update for SQL Server 2019 GDR"
                }
            }
        }

        # Get the latest update for the specified type
        $updates = $updateDatabase[$UpdateType]
        if (!$updates) {
            Write-Log "No updates found for type: $UpdateType" -Level "ERROR"
            return $null
        }

        # Get the latest update (first one in the list)
        $latestUpdateKey = $updates.Keys | Sort-Object -Descending | Select-Object -First 1
        $latestUpdate = $updates[$latestUpdateKey]

        Write-Log "Latest $UpdateType update found: $($latestUpdate.Description)"
        Write-Log "Version: $($latestUpdate.Version)"
        Write-Log "KB: $($latestUpdate.KB)"
        Write-Log "Release Date: $($latestUpdate.ReleaseDate)"

        return $latestUpdate

    } catch {
        Write-Log "Error getting latest SQL update information: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

# Function to compare SQL Server versions
function Compare-SQLVersions {
    param(
        [string]$CurrentVersion,
        [string]$TargetVersion
    )

    try {
        if ([string]::IsNullOrEmpty($CurrentVersion) -or [string]::IsNullOrEmpty($TargetVersion)) {
            return $false
        }

        $current = [Version]$CurrentVersion
        $target = [Version]$TargetVersion

        $result = $current.CompareTo($target)

        Write-Log "Version comparison: Current=$CurrentVersion, Target=$TargetVersion, Result=$result"

        # Return true if current version is less than target (needs update)
        return $result -lt 0

    } catch {
        Write-Log "Error comparing versions: $($_.Exception.Message)" -Level "WARNING"
        return $true # Assume update is needed if comparison fails
    }
}

Write-Log "Starting SQL Server 2019 Latest Update Process"

# Check admin rights
if (!(Test-AdminRights)) {
    Write-Log "This script requires administrator privileges" -Level "ERROR"
    exit 1
}

# Check for pending reboot FIRST
if (Test-PendingReboot) {
    Write-Log "CRITICAL: System restart required before SQL Server update" -Level "ERROR"
    Write-Log "Please restart the computer and run this script again" -Level "ERROR"
    exit 1
}

# Create temp directory if it doesn't exist
if (!(Test-Path "C:\temp")) {
    New-Item -Path "C:\temp" -ItemType Directory -Force | Out-Null
    Write-Log "Created temp directory: C:\temp"
}

# Detect current SQL Server version
Write-Log "Detecting current SQL Server installation..."
$currentVersion = Get-CurrentSQLVersion -Instance $InstanceName

if (!$currentVersion) {
    Write-Log "Could not detect SQL Server 2019 installation for instance: $InstanceName" -Level "ERROR"
    Write-Log "Please verify that SQL Server 2019 is installed and the instance name is correct" -Level "ERROR"
    exit 1
}

Write-Log "Current SQL Server installation detected:"
Write-Log "  Version: $($currentVersion.Version)"
Write-Log "  Edition: $($currentVersion.Edition)"
Write-Log "  Product: $($currentVersion.ProductName)"
Write-Log "  Detection Method: $($currentVersion.DetectionMethod)"

# Get latest available update
Write-Log "Checking for latest SQL Server 2019 $UpdateType update..."
$UpdateInfo = Get-LatestSQLUpdate -UpdateType $UpdateType

if (!$UpdateInfo) {
    Write-Log "Could not retrieve latest update information" -Level "ERROR"
    exit 1
}

# Check if update is needed
$needsUpdate = Compare-SQLVersions -CurrentVersion $currentVersion.Version -TargetVersion $UpdateInfo.Version

if (!$needsUpdate) {
    Write-Log "SQL Server is already up to date!" -Level "INFO"
    Write-Log "Current version: $($currentVersion.Version)"
    Write-Log "Latest available: $($UpdateInfo.Version)"
    Write-Log "No update required. Exiting."
    exit 0
}

Write-Log "Update is available and needed:"
Write-Log "  Current Version: $($currentVersion.Version)"
Write-Log "  Target Version: $($UpdateInfo.Version)"
Write-Log "  Update: $($UpdateInfo.Description)"
Write-Log "  KB Article: $($UpdateInfo.KB)"

# Function to download update with multiple URL attempts
function Get-SQLUpdateFile {
    param(
        [hashtable]$UpdateInfo,
        [string]$DownloadPath
    )

    try {
        # Check if file already exists and validate it
        if (Test-Path $DownloadPath) {
            Write-Log "Update file already exists at: $DownloadPath"

            # Verify file size (basic validation)
            $fileInfo = Get-Item $DownloadPath
            if ($fileInfo.Length -lt 1MB) {
                Write-Log "Downloaded file appears to be incomplete, re-downloading..." -Level "WARNING"
                Remove-Item $DownloadPath -Force
            } else {
                Write-Log "Existing file appears valid (Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB)"
                return $true
            }
        }

        # Build list of download URLs to try
        $downloadUrls = @()

        # Add the direct download URL if available (highest priority)
        if ($UpdateInfo.DirectDownloadUrl) {
            $downloadUrls += $UpdateInfo.DirectDownloadUrl
            Write-Log "Added direct download URL: $($UpdateInfo.DirectDownloadUrl)"
        }

        # Known working download URLs for SQL Server 2019 updates
        # The primary Microsoft download center path for SQL Server 2019
        $primaryDownloadPath = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce"
        $downloadUrls += "$primaryDownloadPath/SQLServer2019-$($UpdateInfo.KB)-x64.exe"

        # Alternative download paths (fallback options)
        $alternativeDownloadPaths = @(
            "https://download.microsoft.com/download/3/8/d/38de7036-2433-4207-8eae-06e247e17b25",
            "https://download.microsoft.com/download/c/4/f/c4f908c9-98ed-4e5f-88d5-7a6ae7b5c0d8",
            "https://download.microsoft.com/download/9/3/2/932cd1d6-8d90-4d5d-9ac0-4e9c1a8f2b8c",
            "https://download.microsoft.com/download/7/c/1/7c14e92e-bdcb-4f89-b7cf-93543e7112d1"
        )

        # Create download URLs for this specific KB
        foreach ($basePath in $alternativeDownloadPaths) {
            $downloadUrls += "$basePath/SQLServer2019-$($UpdateInfo.KB)-x64.exe"
        }

        # Add some alternative naming patterns as last resort
        $downloadUrls += "$primaryDownloadPath/SQL2019-$($UpdateInfo.KB)-x64.exe"

        # Try each URL until one works
        $downloadSuccessful = $false
        foreach ($url in $downloadUrls) {
            try {
                Write-Log "Attempting download from: $url"

                # Use more robust download with progress and timeout
                $webClient = New-Object System.Net.WebClient
                $webClient.DownloadFile($url, $DownloadPath)
                $webClient.Dispose()

                # Verify download
                if (Test-Path $DownloadPath) {
                    $fileInfo = Get-Item $DownloadPath
                    Write-Log "Download successful. File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"

                    if ($fileInfo.Length -gt 1MB) {
                        $downloadSuccessful = $true
                        break
                    } else {
                        Write-Log "Downloaded file too small, trying next URL..." -Level "WARNING"
                        Remove-Item $DownloadPath -Force -ErrorAction SilentlyContinue
                    }
                }
            } catch {
                Write-Log "Download failed from $url : $($_.Exception.Message)" -Level "WARNING"
                Remove-Item $DownloadPath -Force -ErrorAction SilentlyContinue
                continue
            }
        }

        if (!$downloadSuccessful) {
            throw "All download attempts failed. Please check internet connectivity and try again."
        }

        return $true

    } catch {
        Write-Log "Download function failed: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Set download variables
$DownloadPath = "C:\temp\SQLServer2019-$($UpdateInfo.KB)-x64.exe"

## Download the Update Installer
Write-Log "Downloading $($UpdateInfo.Description) from Microsoft Download Center..."
Write-Log "KB Article: $($UpdateInfo.KB)"
Write-Log "Target file: $DownloadPath"

$downloadResult = Get-SQLUpdateFile -UpdateInfo $UpdateInfo -DownloadPath $DownloadPath

if (!$downloadResult) {
    Write-Log "Failed to download the update file after trying multiple URLs" -Level "ERROR"
    Write-Log "" -Level "ERROR"
    Write-Log "MANUAL DOWNLOAD REQUIRED:" -Level "ERROR"
    Write-Log "1. Please manually download the update from: $($UpdateInfo.DownloadUrl)" -Level "ERROR"
    Write-Log "2. Save the file as: $DownloadPath" -Level "ERROR"
    Write-Log "3. Re-run this script to continue with the installation" -Level "ERROR"
    Write-Log "" -Level "ERROR"
    Write-Log "Alternative: Search for '$($UpdateInfo.KB)' on Microsoft Update Catalog" -Level "ERROR"
    Write-Log "URL: https://www.catalog.update.microsoft.com/Search.aspx?q=$($UpdateInfo.KB)" -Level "ERROR"
    exit 1
}

Write-Log "Update file download completed successfully"

# Get service names for the instance
$serviceNames = Get-ServiceNames -Instance $InstanceName
Write-Log "Target instance: $InstanceName"
Write-Log "Engine service: $($serviceNames.Engine)"
Write-Log "Agent service: $($serviceNames.Agent)"

# Function to backup databases (optional)
function Backup-SQLDatabases {
    param([string]$Instance)

    if (!$BackupFirst) {
        Write-Log "Backup not requested, skipping database backup"
        return $true
    }

    try {
        Write-Log "Starting database backup for instance: $Instance"

        $serverName = if ($Instance -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$Instance" }
        $backupPath = "C:\temp\SQLBackups\$Instance"

        if (!(Test-Path $backupPath)) {
            New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        }

        # Use SQLCMD to backup system databases
        $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1

        if ($sqlcmdPath) {
            $backupScript = @"
BACKUP DATABASE [master] TO DISK = '$backupPath\master_backup.bak' WITH INIT;
BACKUP DATABASE [model] TO DISK = '$backupPath\model_backup.bak' WITH INIT;
BACKUP DATABASE [msdb] TO DISK = '$backupPath\msdb_backup.bak' WITH INIT;
"@

            $backupScript | Out-File -FilePath "$backupPath\backup_script.sql" -Force

            & $sqlcmdPath.FullName -S $serverName -E -i "$backupPath\backup_script.sql"
            Write-Log "Database backup completed for instance: $Instance"
            return $true
        }
        else {
            Write-Log "SQLCMD not found, skipping backup" -Level "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Error during backup: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Function to check if services need to be stopped for update
function Test-ServiceStopRequired {
    param([string]$Instance)

    Write-Log "Checking if SQL services need to be stopped for update..."

    # For SQL Server cumulative updates, services typically need to be stopped
    # This is required for file replacement during the update process
    $serviceNames = Get-ServiceNames -Instance $Instance

    $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
    if ($engineService -and $engineService.Status -eq "Running") {
        Write-Log "SQL Server Engine is running - services will need to be stopped for update"
        return $true
    } else {
        Write-Log "SQL Server Engine is not running - no need to stop services"
        return $false
    }
}

# Function to wait for service to reach desired status
function Wait-ForServiceStatus {
    param(
        [string]$ServiceName,
        [string]$DesiredStatus,
        [int]$TimeoutSeconds = 60
    )

    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)

    do {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq $DesiredStatus) {
            return $true
        }
        Start-Sleep -Seconds 2
    } while ((Get-Date) -lt $timeout)

    return $false
}

# Function to check for pending reboot
function Test-PendingReboot {
    try {
        Write-Log "Checking for pending reboot..."

        $pendingReboot = $false

        # Check registry for pending reboot
        $regKeys = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired",
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Component Based Servicing\RebootPending",
            "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager"
        )

        foreach ($key in $regKeys) {
            if (Test-Path $key) {
                if ($key -like "*Session Manager*") {
                    $sessionManager = Get-ItemProperty -Path $key -ErrorAction SilentlyContinue
                    if ($sessionManager.PendingFileRenameOperations) {
                        $pendingReboot = $true
                        break
                    }
                } else {
                    $pendingReboot = $true
                    break
                }
            }
        }

        if ($pendingReboot) {
            Write-Log "WARNING: System has pending reboot!" -Level "ERROR"
            Write-Log "SQL Server installation will fail until system is restarted" -Level "ERROR"
            Write-Log "Please restart the computer and run this script again" -Level "ERROR"
            return $true
        } else {
            Write-Log "No pending reboot detected"
            return $false
        }

    } catch {
        Write-Log "Error checking for pending reboot: $($_.Exception.Message)" -Level "WARNING"
        return $false
    }
}

# Function to stop all SQL Server related services
function Stop-AllSQLServerServices {
    param([string]$Instance)

    $serviceNames = Get-ServiceNames -Instance $Instance
    $servicesStopped = @()

    try {
        Write-Log "Stopping all SQL Server related services for update..."

        # List of all SQL Server services to stop
        $sqlServiceNames = @(
            $serviceNames.Agent,
            $serviceNames.Engine,
            "SQLBrowser",
            "SQLTELEMETRY",
            "SQLWriter",
            "SQLServerReportingServices"
        )

        foreach ($serviceName in $sqlServiceNames) {
            $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
            if ($service -and $service.Status -eq "Running") {
                Write-Log "Stopping service: $serviceName"
                try {
                    Stop-Service -Name $serviceName -Force -ErrorAction Stop

                    # Wait for service to stop
                    if (Wait-ForServiceStatus -ServiceName $serviceName -DesiredStatus "Stopped" -TimeoutSeconds 60) {
                        Write-Log "Service stopped successfully: $serviceName"
                        $servicesStopped += $serviceName
                    } else {
                        Write-Log "Service did not stop within timeout: $serviceName" -Level "WARNING"
                        $servicesStopped += $serviceName
                    }
                } catch {
                    Write-Log "Error stopping service $serviceName : $($_.Exception.Message)" -Level "WARNING"
                }
            } else {
                Write-Log "Service not running or not found: $serviceName"
            }
        }

        # Kill any remaining SQL Server processes
        Write-Log "Checking for remaining SQL Server processes..."
        $sqlProcesses = Get-Process | Where-Object {
            $_.ProcessName -like "*sql*" -and
            $_.ProcessName -ne "mysqld" -and  # Exclude MySQL
            $_.ProcessName -ne "sqlcmd"       # Exclude SQL command line tools
        }

        foreach ($process in $sqlProcesses) {
            try {
                Write-Log "Terminating SQL Server process: $($process.ProcessName) (PID: $($process.Id))"
                $process.Kill()
                Start-Sleep -Seconds 2
            } catch {
                Write-Log "Could not terminate process $($process.ProcessName): $($_.Exception.Message)" -Level "WARNING"
            }
        }

        # Wait a moment for everything to fully stop
        Start-Sleep -Seconds 10

        return $servicesStopped

    } catch {
        Write-Log "Error stopping SQL Server services: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

# Backup databases if requested (only if services are running)
if ($BackupFirst) {
    $needsServiceStop = Test-ServiceStopRequired -Instance $InstanceName
    if ($needsServiceStop) {
        Write-Log "Performing backup before stopping services..."
        if (!(Backup-SQLDatabases -Instance $InstanceName)) {
            Write-Log "Backup failed, but continuing with update..." -Level "WARNING"
        }
    } else {
        Write-Log "SQL Server services not running - skipping backup"
    }
}

## Stop SQL Server Services (only if required)
$stoppedServices = @()
$needsServiceStop = Test-ServiceStopRequired -Instance $InstanceName

if ($needsServiceStop) {
    try {
        $stoppedServices = Stop-AllSQLServerServices -Instance $InstanceName
        Write-Log "All SQL Server services stopped successfully. Stopped services: $($stoppedServices -join ', ')"
    } catch {
        Write-Log "Failed to stop SQL Server services: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
} else {
    Write-Log "SQL Server services are not running - proceeding with update without stopping services"
}

# Function to check if current update requires service restart
function Test-UpdateRequiresServiceRestart {
    param([string]$UpdateType)

    Write-Log "Checking if this update type requires SQL service restart..."

    # Different update types have different requirements
    switch ($UpdateType.ToUpper()) {
        "CU" {
            Write-Log "Cumulative Update detected - service restart will be required"
            return $true
        }
        "GDR" {
            Write-Log "General Distribution Release (Security Update) detected - service restart will be required"
            return $true
        }
        default {
            Write-Log "Unknown update type - assuming service restart is required"
            return $true
        }
    }
}

## Install the Update
Write-Log "Starting installation of $($UpdateInfo.Description)..."
Write-Log "Installing update: $($UpdateInfo.KB)"
Write-Log "Target version: $($UpdateInfo.Version)"

# Check if this update requires service restart
$requiresServiceRestart = Test-UpdateRequiresServiceRestart -UpdateType $UpdateType
Write-Log "Service restart required for this update: $requiresServiceRestart"

try {
    # Enhanced installation arguments for better compatibility and error reduction
    $installArgs = @(
        "/quiet",                                    # Silent installation
        "/IAcceptSQLServerLicenseTerms",            # Accept license terms
        "/Action=Patch",                            # Specify patch action
        "/IACCEPTSQLSERVERLICENSETERMS",            # Ensure license acceptance (duplicate for safety)
        "/SUPPRESSPRIVACYSTATEMENTNOTICE=True",     # Suppress privacy notice
        "/NORESTART",                               # Prevent automatic restart
        "/ERRORREPORTING=False",                    # Disable error reporting
        "/SQMREPORTING=False"                       # Disable SQM reporting
    )

    # Add instance-specific arguments
    if ($InstanceName -eq "MSSQLSERVER") {
        $installArgs += "/AllInstances"             # Apply to all instances for default
    } else {
        $installArgs += "/InstanceName=$InstanceName"  # Apply to specific named instance
    }

    Write-Log "Installation arguments: $($installArgs -join ' ')"
    Write-Log "Target instance: $InstanceName"
    Write-Log "Services stopped for update: $($stoppedServices.Count -gt 0)"

    $process = Start-Process -FilePath $DownloadPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow

    Write-Log "Installation process completed with exit code: $($process.ExitCode)"

    # Handle different exit codes
    switch ($process.ExitCode) {
        0 {
            Write-Log "Installation completed successfully"
        }
        3010 {
            Write-Log "Installation completed successfully - System reboot required" -Level "WARNING"
        }
        1641 {
            Write-Log "Installation completed successfully - Installer initiated reboot" -Level "WARNING"
        }
        default {
            Write-Log "Installation failed with exit code: $($process.ExitCode)" -Level "ERROR"

            # Common exit codes and their meanings
            $exitCodeMeanings = @{
                1603 = "Fatal error during installation"
                1619 = "Installation package could not be opened"
                1620 = "Installation package could not be opened (corrupt)"
                1633 = "Installation package is not supported on this platform"
                1638 = "Another version of this product is already installed"
            }

            if ($exitCodeMeanings.ContainsKey($process.ExitCode)) {
                Write-Log "Exit code meaning: $($exitCodeMeanings[$process.ExitCode])" -Level "ERROR"
            }

            throw "Installation failed with exit code: $($process.ExitCode)"
        }
    }

} catch {
    Write-Log "An error occurred during installation: $($_.Exception.Message)" -Level "ERROR"

    # If services were stopped, try to restart them after installation failure
    if ($stoppedServices.Count -gt 0) {
        Write-Log "Attempting to restart services after installation failure..." -Level "WARNING"
        try {
            Start-SQLServerServices -Instance $InstanceName -ServicesToStart $stoppedServices
            Write-Log "Services restarted after installation failure"
        } catch {
            Write-Log "Failed to restart services after installation failure - manual intervention required" -Level "ERROR"
        }
    }
    exit 1
}

# Function to start SQL Server services (only those that were stopped)
function Start-SQLServerServices {
    param(
        [string]$Instance,
        [array]$ServicesToStart
    )

    if ($ServicesToStart.Count -eq 0) {
        Write-Log "No services to start - they were not stopped by this script"
        return
    }

    $serviceNames = Get-ServiceNames -Instance $Instance

    try {
        Write-Log "Starting SQL Server services that were stopped..."

        # Start SQL Server Engine first (if it was stopped)
        if ($ServicesToStart -contains $serviceNames.Engine) {
            Write-Log "Starting SQL Server Engine: $($serviceNames.Engine)"
            Start-Service -Name $serviceNames.Engine

            # Wait for service to start
            if (Wait-ForServiceStatus -ServiceName $serviceNames.Engine -DesiredStatus "Running" -TimeoutSeconds 120) {
                Write-Log "SQL Server Engine started successfully"
            } else {
                Write-Log "SQL Server Engine did not start within timeout" -Level "WARNING"
            }

            # Wait for engine to fully initialize
            Write-Log "Waiting for SQL Server Engine to fully initialize..."
            Start-Sleep -Seconds 15
        }

        # Start SQL Server Agent (if it was stopped)
        if ($ServicesToStart -contains $serviceNames.Agent) {
            Write-Log "Starting SQL Server Agent: $($serviceNames.Agent)"
            $agentService = Get-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue
            if ($agentService) {
                Start-Service -Name $serviceNames.Agent

                # Wait for service to start
                if (Wait-ForServiceStatus -ServiceName $serviceNames.Agent -DesiredStatus "Running" -TimeoutSeconds 60) {
                    Write-Log "SQL Server Agent started successfully"
                } else {
                    Write-Log "SQL Server Agent did not start within timeout" -Level "WARNING"
                }
            } else {
                Write-Log "SQL Server Agent service not found" -Level "WARNING"
            }
        }

    } catch {
        Write-Log "Error starting SQL Server services: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

## Restart SQL Server Services (only those that were stopped)
if ($stoppedServices.Count -gt 0) {
    Write-Log "Restarting SQL Server services that were stopped for the update..."
    try {
        Start-SQLServerServices -Instance $InstanceName -ServicesToStart $stoppedServices
        Write-Log "All stopped services have been restarted successfully"
    } catch {
        Write-Log "Failed to restart SQL Server services: $($_.Exception.Message)" -Level "ERROR"

        # Attempt emergency restart of critical services
        Write-Log "Attempting emergency restart of SQL Server Engine..." -Level "WARNING"
        try {
            $serviceNames = Get-ServiceNames -Instance $InstanceName
            Start-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
            Write-Log "Emergency restart of SQL Server Engine completed"
        } catch {
            Write-Log "Emergency restart failed - manual intervention may be required" -Level "ERROR"
        }
        exit 1
    }
} else {
    Write-Log "No services were stopped by this script - no restart required"
}

## Post-installation verification
Write-Log "Performing post-installation verification..."
try {
    # Check service status
    $serviceNames = Get-ServiceNames -Instance $InstanceName
    $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
    if ($engineService -and $engineService.Status -eq "Running") {
        Write-Log "SQL Server Engine is running successfully"
    } else {
        Write-Log "SQL Server Engine is not running properly" -Level "WARNING"
    }

    # Wait a moment for SQL Server to fully initialize
    Start-Sleep -Seconds 10

    # Verify the update was successful by checking the new version
    Write-Log "Verifying update installation..."
    $updatedVersion = Get-CurrentSQLVersion -Instance $InstanceName

    if ($updatedVersion) {
        Write-Log "Post-update version detection successful:"
        Write-Log "  New Version: $($updatedVersion.Version)"
        Write-Log "  Edition: $($updatedVersion.Edition)"

        # Compare with target version
        if ($updatedVersion.Version -eq $UpdateInfo.Version) {
            Write-Log "✅ UPDATE SUCCESSFUL! SQL Server has been updated to version $($updatedVersion.Version)" -Level "INFO"
        } elseif ($updatedVersion.Version -gt $currentVersion.Version) {
            Write-Log "✅ UPDATE SUCCESSFUL! SQL Server has been updated from $($currentVersion.Version) to $($updatedVersion.Version)" -Level "INFO"
        } else {
            Write-Log "⚠️ UPDATE STATUS UNCLEAR: Version appears unchanged. This may be normal for some update types." -Level "WARNING"
            Write-Log "  Previous Version: $($currentVersion.Version)"
            Write-Log "  Current Version: $($updatedVersion.Version)"
            Write-Log "  Target Version: $($UpdateInfo.Version)"
        }
    } else {
        Write-Log "Could not verify updated SQL Server version" -Level "WARNING"
    }

    # Try to get detailed SQL Server version using SQLCMD
    $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($sqlcmdPath) {
        $serverName = if ($InstanceName -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$InstanceName" }

        try {
            # Get detailed version information
            $versionQuery = "SELECT @@VERSION AS VersionInfo, SERVERPROPERTY('ProductVersion') AS ProductVersion, SERVERPROPERTY('ProductLevel') AS ProductLevel"
            $result = & $sqlcmdPath.FullName -S $serverName -E -Q $versionQuery -h -1 2>$null

            if ($result) {
                Write-Log "Detailed SQL Server version information:"
                $result | ForEach-Object {
                    if ($_.Trim()) {
                        Write-Log "  $($_.Trim())"
                    }
                }
            }
        } catch {
            Write-Log "Could not retrieve detailed version information via SQLCMD" -Level "WARNING"
        }
    }

} catch {
    Write-Log "Post-installation verification encountered issues: $($_.Exception.Message)" -Level "WARNING"
}

## Cleanup
Write-Log "Performing cleanup..."
try {
    # Cleanup the downloaded installer file
    if (Test-Path $DownloadPath) {
        Remove-Item -Path $DownloadPath -Force
        Write-Log "Installer file deleted successfully: $DownloadPath"
    }

    Write-Log "✅ SQL Server 2019 update process completed successfully!"
    Write-Log "Summary:"
    Write-Log "  Instance: $InstanceName"
    Write-Log "  Update Applied: $($UpdateInfo.Description)"
    Write-Log "  KB Article: $($UpdateInfo.KB)"
    Write-Log "  Previous Version: $($currentVersion.Version)"
    if ($updatedVersion) {
        Write-Log "  New Version: $($updatedVersion.Version)"
    }
    Write-Log "  Services Restarted: $($stoppedServices.Count -gt 0)"
    Write-Log "  Backup Performed: $BackupFirst"

} catch {
    Write-Log "Cleanup failed: $($_.Exception.Message)" -Level "WARNING"
}

Write-Log "Script execution finished. Check log file for details: $LogPath"
Write-Log "=================================================="