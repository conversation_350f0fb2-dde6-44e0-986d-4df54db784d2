﻿# Define variables
$DownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5054833-x64.exe"
$DownloadPath = "$env:TEMP\SQLServer2019-CU32-x64.exe"
$InstanceName = "MSSQLSERVER" # Change this if you have a named instance
$ServiceName = "MSSQLSERVER" # Change to 'MSSQL$<InstanceName>' for a named instance

# Create a temporary directory for the download if it doesn't exist
if (-not (Test-Path "$env:TEMP")) {
    New-Item -Path "$env:TEMP" -ItemType Directory | Out-Null
}

## Download the CU Installer
Write-Host "Downloading SQL Server 2019 CU32 from the Microsoft Download Center..."
try {
    Invoke-WebRequest -Uri $DownloadUrl -OutFile $DownloadPath -UseBasicParsing
    Write-Host "Download complete. File saved to: $DownloadPath"
} catch {
    Write-Host "Failed to download the file. Please check the URL and your internet connection."
    exit
}

## Stop SQL Server Service
Write-Host "Checking for SQL Server service and stopping it..."
try {
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        if ($service.Status -eq "Running") {
            Write-Host "Stopping service '$ServiceName'..."
            Stop-Service -Name $ServiceName -Force
            # Wait for the service to stop
            Wait-Service -Name $ServiceName -Timeout 60
            Write-Host "Service '$ServiceName' stopped successfully."
        } else {
            Write-Host "Service '$ServiceName' is already stopped."
        }
    } else {
        Write-Host "SQL Server service '$ServiceName' not found."
        exit
    }
} catch {
    Write-Host "Failed to stop the SQL Server service. Please check the service name and try again."
    exit
}

## Install the CU
Write-Host "Starting the installation of SQL Server CU32..."
try {
    # The /q switch runs the installer in quiet mode (no UI) and /IAcceptSQLServerLicenseTerms is required
    Start-Process -FilePath $DownloadPath -ArgumentList "/q /IAcceptSQLServerLicenseTerms /update" -Wait -Passthru
    Write-Host "Installation completed. Checking for pending reboot..."
} catch {
    Write-Host "An error occurred during the installation."
    exit
}

## Restart SQL Server Service and Cleanup
Write-Host "Restarting SQL Server service '$ServiceName'..."
try {
    Start-Service -Name $ServiceName
    Wait-Service -Name $ServiceName -Timeout 60
    Write-Host "Service '$ServiceName' restarted successfully."
    
    # Cleanup the downloaded installer file
    Remove-Item -Path $DownloadPath -Force
    Write-Host "Installer file deleted. Script finished."

} catch {
    Write-Host "Failed to restart the SQL Server service."
    exit
}