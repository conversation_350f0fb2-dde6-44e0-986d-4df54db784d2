# SQL Server 2019 Update Script - Enhanced Version
# This script downloads and installs SQL Server 2019 CU32 with improved error handling and logging

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER", # Default instance or specify named instance

    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst,

    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\temp\SQLServer2019Update.log"
)

# Define variables
$DownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-*********-x64.exe"
$DownloadPath = "C:\temp\SQLServer2019-CU32-x64.exe"

# Function to write log entries
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    Write-Host $logEntry

    # Ensure log directory exists
    $logDir = Split-Path $LogPath -Parent
    if (!(Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }

    Add-Content -Path $LogPath -Value $logEntry -Force
}

# Function to check if user has admin rights
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to get service names based on instance
function Get-ServiceNames {
    param([string]$Instance)

    if ($Instance -eq "MSSQLSERVER") {
        return @{
            Engine = "MSSQLSERVER"
            Agent = "SQLSERVERAGENT"
        }
    } else {
        return @{
            Engine = "MSSQL`$$Instance"
            Agent = "SQLAgent`$$Instance"
        }
    }
}

Write-Log "Starting SQL Server 2019 CU32 Update Process"

# Check admin rights
if (!(Test-AdminRights)) {
    Write-Log "This script requires administrator privileges" -Level "ERROR"
    exit 1
}

# Create temp directory if it doesn't exist
if (!(Test-Path "C:\temp")) {
    New-Item -Path "C:\temp" -ItemType Directory -Force | Out-Null
    Write-Log "Created temp directory: C:\temp"
}

## Download the CU Installer
Write-Log "Downloading SQL Server 2019 CU32 from Microsoft Download Center..."
try {
    # Check if file already exists
    if (Test-Path $DownloadPath) {
        Write-Log "Update file already exists at: $DownloadPath"
    } else {
        Invoke-WebRequest -Uri $DownloadUrl -OutFile $DownloadPath -UseBasicParsing
        Write-Log "Download complete. File saved to: $DownloadPath"
    }

    # Verify file was downloaded
    if (!(Test-Path $DownloadPath)) {
        throw "Downloaded file not found at expected location"
    }
} catch {
    Write-Log "Failed to download the file: $($_.Exception.Message)" -Level "ERROR"
    exit 1
}

# Get service names for the instance
$serviceNames = Get-ServiceNames -Instance $InstanceName
Write-Log "Target instance: $InstanceName"
Write-Log "Engine service: $($serviceNames.Engine)"
Write-Log "Agent service: $($serviceNames.Agent)"

# Function to backup databases (optional)
function Backup-SQLDatabases {
    param([string]$Instance)

    if (!$BackupFirst) {
        Write-Log "Backup not requested, skipping database backup"
        return $true
    }

    try {
        Write-Log "Starting database backup for instance: $Instance"

        $serverName = if ($Instance -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$Instance" }
        $backupPath = "C:\temp\SQLBackups\$Instance"

        if (!(Test-Path $backupPath)) {
            New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        }

        # Use SQLCMD to backup system databases
        $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1

        if ($sqlcmdPath) {
            $backupScript = @"
BACKUP DATABASE [master] TO DISK = '$backupPath\master_backup.bak' WITH INIT;
BACKUP DATABASE [model] TO DISK = '$backupPath\model_backup.bak' WITH INIT;
BACKUP DATABASE [msdb] TO DISK = '$backupPath\msdb_backup.bak' WITH INIT;
"@

            $backupScript | Out-File -FilePath "$backupPath\backup_script.sql" -Force

            & $sqlcmdPath.FullName -S $serverName -E -i "$backupPath\backup_script.sql"
            Write-Log "Database backup completed for instance: $Instance"
            return $true
        }
        else {
            Write-Log "SQLCMD not found, skipping backup" -Level "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Error during backup: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Backup databases if requested
if ($BackupFirst) {
    if (!(Backup-SQLDatabases -Instance $InstanceName)) {
        Write-Log "Backup failed, but continuing with update..." -Level "WARNING"
    }
}

## Stop SQL Server Services
Write-Log "Checking and stopping SQL Server services..."
try {
    # Stop SQL Server Agent first
    $agentService = Get-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue
    if ($agentService) {
        if ($agentService.Status -eq "Running") {
            Write-Log "Stopping SQL Server Agent: $($serviceNames.Agent)"
            Stop-Service -Name $serviceNames.Agent -Force
            Wait-Service -Name $serviceNames.Agent -Status Stopped -Timeout 60
            Write-Log "SQL Server Agent stopped successfully"
        } else {
            Write-Log "SQL Server Agent is already stopped"
        }
    } else {
        Write-Log "SQL Server Agent service not found: $($serviceNames.Agent)" -Level "WARNING"
    }

    # Stop SQL Server Engine
    $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
    if ($engineService) {
        if ($engineService.Status -eq "Running") {
            Write-Log "Stopping SQL Server Engine: $($serviceNames.Engine)"
            Stop-Service -Name $serviceNames.Engine -Force
            Wait-Service -Name $serviceNames.Engine -Status Stopped -Timeout 60
            Write-Log "SQL Server Engine stopped successfully"
        } else {
            Write-Log "SQL Server Engine is already stopped"
        }
    } else {
        Write-Log "SQL Server Engine service not found: $($serviceNames.Engine)" -Level "ERROR"
        exit 1
    }
} catch {
    Write-Log "Failed to stop SQL Server services: $($_.Exception.Message)" -Level "ERROR"
    exit 1
}

## Install the CU
Write-Log "Starting installation of SQL Server 2019 CU32..."
try {
    # Enhanced installation arguments for better compatibility
    $installArgs = @(
        "/quiet",                           # Silent installation
        "/IAcceptSQLServerLicenseTerms",   # Accept license terms
        "/Action=Patch",                   # Specify patch action
        "/AllInstances"                    # Apply to all instances (or use /InstanceName for specific instance)
    )

    # If specific instance, use InstanceName parameter instead of AllInstances
    if ($InstanceName -ne "MSSQLSERVER") {
        $installArgs = $installArgs | Where-Object { $_ -ne "/AllInstances" }
        $installArgs += "/InstanceName=$InstanceName"
    }

    Write-Log "Installation arguments: $($installArgs -join ' ')"

    $process = Start-Process -FilePath $DownloadPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow

    if ($process.ExitCode -eq 0) {
        Write-Log "Installation completed successfully"
    } elseif ($process.ExitCode -eq 3010) {
        Write-Log "Installation completed successfully - Reboot required" -Level "WARNING"
    } else {
        Write-Log "Installation failed with exit code: $($process.ExitCode)" -Level "ERROR"
        throw "Installation failed with exit code: $($process.ExitCode)"
    }
} catch {
    Write-Log "An error occurred during installation: $($_.Exception.Message)" -Level "ERROR"

    # Try to start services anyway in case of partial failure
    Write-Log "Attempting to restart services after installation failure..."
    try {
        Start-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
        Start-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue
    } catch {
        Write-Log "Failed to restart services after installation failure" -Level "ERROR"
    }
    exit 1
}

## Restart SQL Server Services
Write-Log "Restarting SQL Server services..."
try {
    # Start SQL Server Engine first
    Write-Log "Starting SQL Server Engine: $($serviceNames.Engine)"
    Start-Service -Name $serviceNames.Engine
    Wait-Service -Name $serviceNames.Engine -Status Running -Timeout 120
    Write-Log "SQL Server Engine started successfully"

    # Wait a bit then start Agent
    Start-Sleep -Seconds 10

    # Start SQL Server Agent
    Write-Log "Starting SQL Server Agent: $($serviceNames.Agent)"
    $agentService = Get-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue
    if ($agentService) {
        Start-Service -Name $serviceNames.Agent
        Wait-Service -Name $serviceNames.Agent -Status Running -Timeout 60
        Write-Log "SQL Server Agent started successfully"
    } else {
        Write-Log "SQL Server Agent service not found, skipping" -Level "WARNING"
    }

} catch {
    Write-Log "Failed to restart SQL Server services: $($_.Exception.Message)" -Level "ERROR"
    exit 1
}

## Post-installation verification
Write-Log "Performing post-installation verification..."
try {
    # Check service status
    $engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
    if ($engineService -and $engineService.Status -eq "Running") {
        Write-Log "SQL Server Engine is running successfully"
    } else {
        Write-Log "SQL Server Engine is not running properly" -Level "WARNING"
    }

    # Try to get SQL Server version using SQLCMD
    $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($sqlcmdPath) {
        $serverName = if ($InstanceName -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$InstanceName" }
        $versionQuery = "SELECT @@VERSION"

        try {
            $version = & $sqlcmdPath.FullName -S $serverName -E -Q $versionQuery -h -1 2>$null
            if ($version) {
                Write-Log "SQL Server version verification successful"
                Write-Log "Version info: $($version.Trim())"
            }
        } catch {
            Write-Log "Could not verify SQL Server version, but services are running" -Level "WARNING"
        }
    }
} catch {
    Write-Log "Post-installation verification encountered issues: $($_.Exception.Message)" -Level "WARNING"
}

## Cleanup
Write-Log "Performing cleanup..."
try {
    # Cleanup the downloaded installer file
    if (Test-Path $DownloadPath) {
        Remove-Item -Path $DownloadPath -Force
        Write-Log "Installer file deleted successfully"
    }

    Write-Log "SQL Server 2019 CU32 update process completed successfully"

} catch {
    Write-Log "Cleanup failed: $($_.Exception.Message)" -Level "WARNING"
}

Write-Log "Script execution finished. Check log file for details: $LogPath"