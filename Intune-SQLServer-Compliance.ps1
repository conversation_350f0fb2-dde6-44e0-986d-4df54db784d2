# SQL Server Compliance Detection Script for Intune
# This script checks SQL Server versions against compliance requirements

param(
    [Parameter(Mandatory=$false)]
    [string]$MinimumVersion = "2017", # Minimum supported SQL Server version
    
    [Parameter(Mandatory=$false)]
    [string[]]$RequiredVersions = @("2017", "2019", "2022"), # Allowed SQL Server versions
    
    [Parameter(Mandatory=$false)]
    [switch]$CheckSecurityUpdates
)

# Function to convert version string to comparable number
function ConvertTo-VersionNumber {
    param([string]$VersionString)
    
    if ([string]::IsNullOrEmpty($VersionString) -or $VersionString -eq "Unknown") {
        return 0
    }
    
    try {
        $parts = $VersionString.Split('.')
        $major = [int]$parts[0]
        $minor = if ($parts.Length -gt 1) { [int]$parts[1] } else { 0 }
        $build = if ($parts.Length -gt 2) { [int]$parts[2] } else { 0 }
        $revision = if ($parts.Length -gt 3) { [int]$parts[3] } else { 0 }
        
        return ($major * 1000000) + ($minor * 10000) + ($build * 100) + $revision
    }
    catch {
        return 0
    }
}

# Function to get minimum version number for comparison
function Get-MinimumVersionNumber {
    param([string]$MinVersion)
    
    $versionMap = @{
        "2022" = "16.0.0.0"
        "2019" = "15.0.0.0"
        "2017" = "14.0.0.0"
        "2016" = "13.0.0.0"
        "2014" = "12.0.0.0"
        "2012" = "11.0.0.0"
        "2008R2" = "10.5.0.0"
        "2008" = "10.0.0.0"
    }
    
    $versionString = $versionMap[$MinVersion]
    if ($versionString) {
        return ConvertTo-VersionNumber -VersionString $versionString
    }
    
    return ConvertTo-VersionNumber -VersionString $MinVersion
}

# Function to check if version is in security support
function Test-SecuritySupport {
    param([string]$ProductName, [string]$Version)
    
    # SQL Server versions and their support status (as of 2024)
    $supportMatrix = @{
        "SQL Server 2022" = @{ InSupport = $true; ExtendedSupport = $false }
        "SQL Server 2019" = @{ InSupport = $true; ExtendedSupport = $false }
        "SQL Server 2017" = @{ InSupport = $true; ExtendedSupport = $false }
        "SQL Server 2016" = @{ InSupport = $false; ExtendedSupport = $true }
        "SQL Server 2014" = @{ InSupport = $false; ExtendedSupport = $true }
        "SQL Server 2012" = @{ InSupport = $false; ExtendedSupport = $false }
        "SQL Server 2008 R2" = @{ InSupport = $false; ExtendedSupport = $false }
        "SQL Server 2008" = @{ InSupport = $false; ExtendedSupport = $false }
    }
    
    $support = $supportMatrix[$ProductName]
    if ($support) {
        return @{
            InMainstreamSupport = $support.InSupport
            InExtendedSupport = $support.ExtendedSupport
            HasSecuritySupport = $support.InSupport -or $support.ExtendedSupport
        }
    }
    
    return @{
        InMainstreamSupport = $false
        InExtendedSupport = $false
        HasSecuritySupport = $false
    }
}

# Main compliance check
try {
    # Run the detection script to get current SQL Server instances
    $detectionScript = Join-Path $PSScriptRoot "Detect-SQLServerVersion.ps1"
    
    if (!(Test-Path $detectionScript)) {
        Write-Output "DETECTION_SCRIPT_NOT_FOUND"
        exit 1
    }
    
    # Execute detection script
    & $detectionScript -ExportToCSV | Out-Null
    
    # Read compliance results
    $complianceFile = "C:\temp\SQLServerCompliance.json"
    if (!(Test-Path $complianceFile)) {
        Write-Output "NO_SQL_INSTANCES_FOUND"
        exit 0
    }
    
    $complianceData = Get-Content $complianceFile | ConvertFrom-Json
    $instances = $complianceData.Instances
    
    if ($instances.Count -eq 0) {
        Write-Output "NO_SQL_INSTANCES_FOUND"
        exit 0
    }
    
    # Initialize compliance results
    $complianceResults = @{
        DeviceName = $env:COMPUTERNAME
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        TotalInstances = $instances.Count
        CompliantInstances = 0
        NonCompliantInstances = 0
        UnsupportedInstances = 0
        ComplianceStatus = "COMPLIANT"
        Details = @()
    }
    
    $minVersionNumber = Get-MinimumVersionNumber -MinVersion $MinimumVersion
    
    # Check each instance for compliance
    foreach ($instance in $instances) {
        $instanceVersionNumber = ConvertTo-VersionNumber -VersionString $instance.Version
        $supportInfo = Test-SecuritySupport -ProductName $instance.ProductName -Version $instance.Version
        
        $instanceCompliance = @{
            InstanceName = $instance.InstanceName
            ProductName = $instance.ProductName
            Version = $instance.Version
            Edition = $instance.Edition
            IsCompliant = $false
            ComplianceReason = ""
            SecuritySupport = $supportInfo
        }
        
        # Check version compliance
        if ($instanceVersionNumber -eq 0) {
            $instanceCompliance.IsCompliant = $false
            $instanceCompliance.ComplianceReason = "Unknown version detected"
            $complianceResults.NonCompliantInstances++
        }
        elseif ($instanceVersionNumber -lt $minVersionNumber) {
            $instanceCompliance.IsCompliant = $false
            $instanceCompliance.ComplianceReason = "Version below minimum required ($MinimumVersion)"
            $complianceResults.NonCompliantInstances++
        }
        elseif ($CheckSecurityUpdates -and !$supportInfo.HasSecuritySupport) {
            $instanceCompliance.IsCompliant = $false
            $instanceCompliance.ComplianceReason = "Version no longer receives security updates"
            $complianceResults.UnsupportedInstances++
        }
        else {
            # Check if version is in allowed list
            $versionAllowed = $false
            foreach ($allowedVersion in $RequiredVersions) {
                $allowedVersionNumber = Get-MinimumVersionNumber -MinVersion $allowedVersion
                if ($instanceVersionNumber -ge $allowedVersionNumber) {
                    $versionAllowed = $true
                    break
                }
            }
            
            if ($versionAllowed) {
                $instanceCompliance.IsCompliant = $true
                $instanceCompliance.ComplianceReason = "Version meets compliance requirements"
                $complianceResults.CompliantInstances++
            }
            else {
                $instanceCompliance.IsCompliant = $false
                $instanceCompliance.ComplianceReason = "Version not in approved list"
                $complianceResults.NonCompliantInstances++
            }
        }
        
        $complianceResults.Details += $instanceCompliance
    }
    
    # Determine overall compliance status
    if ($complianceResults.NonCompliantInstances -gt 0 -or $complianceResults.UnsupportedInstances -gt 0) {
        $complianceResults.ComplianceStatus = "NON_COMPLIANT"
    }
    
    # Output results for Intune
    $outputPath = "C:\temp\SQLServerComplianceResults.json"
    $complianceResults | ConvertTo-Json -Depth 4 | Out-File -FilePath $outputPath -Force
    
    # Output compliance status for Intune detection
    if ($complianceResults.ComplianceStatus -eq "COMPLIANT") {
        Write-Output "COMPLIANT"
        exit 0
    }
    else {
        Write-Output "NON_COMPLIANT"
        
        # Output summary for logging
        Write-Host "Compliance Summary:"
        Write-Host "Total Instances: $($complianceResults.TotalInstances)"
        Write-Host "Compliant: $($complianceResults.CompliantInstances)"
        Write-Host "Non-Compliant: $($complianceResults.NonCompliantInstances)"
        Write-Host "Unsupported: $($complianceResults.UnsupportedInstances)"
        
        foreach ($detail in $complianceResults.Details) {
            if (!$detail.IsCompliant) {
                Write-Host "- $($detail.InstanceName): $($detail.ComplianceReason)"
            }
        }
        
        exit 1
    }
}
catch {
    Write-Output "COMPLIANCE_CHECK_ERROR: $($_.Exception.Message)"
    exit 1
}
