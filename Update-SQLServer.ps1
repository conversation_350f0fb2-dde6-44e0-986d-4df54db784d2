# SQL Server Update Script for Intune
# This script updates SQL Server instances to the latest supported version

param(
    [Parameter(Mandatory=$false)]
    [string]$TargetVersion = "2022",
    
    [Parameter(Mandatory=$false)]
    [string]$UpdatePackagePath,
    
    [Parameter(Mandatory=$false)]
    [switch]$ForceUpdate,
    
    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst,
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\temp\SQLServerUpdate.log"
)

# Function to write log entries
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Write-Host $logEntry
    Add-Content -Path $LogPath -Value $logEntry -Force
}

# Function to check if user has admin rights
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to stop SQL Server services
function Stop-SQLServices {
    param([string]$InstanceName)
    
    try {
        $serviceName = if ($InstanceName -eq "DEFAULT") { "MSSQLSERVER" } else { "MSSQL`$$InstanceName" }
        $agentService = if ($InstanceName -eq "DEFAULT") { "SQLSERVERAGENT" } else { "SQLAgent`$$InstanceName" }
        
        Write-Log "Stopping SQL Server services for instance: $InstanceName"
        
        # Stop SQL Server Agent first
        $agent = Get-Service -Name $agentService -ErrorAction SilentlyContinue
        if ($agent -and $agent.Status -eq "Running") {
            Stop-Service -Name $agentService -Force
            Write-Log "Stopped SQL Server Agent: $agentService"
        }
        
        # Stop SQL Server Engine
        $engine = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($engine -and $engine.Status -eq "Running") {
            Stop-Service -Name $serviceName -Force
            Write-Log "Stopped SQL Server Engine: $serviceName"
        }
        
        return $true
    }
    catch {
        Write-Log "Error stopping SQL services: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Function to start SQL Server services
function Start-SQLServices {
    param([string]$InstanceName)
    
    try {
        $serviceName = if ($InstanceName -eq "DEFAULT") { "MSSQLSERVER" } else { "MSSQL`$$InstanceName" }
        $agentService = if ($InstanceName -eq "DEFAULT") { "SQLSERVERAGENT" } else { "SQLAgent`$$InstanceName" }
        
        Write-Log "Starting SQL Server services for instance: $InstanceName"
        
        # Start SQL Server Engine first
        Start-Service -Name $serviceName
        Write-Log "Started SQL Server Engine: $serviceName"
        
        # Wait a bit then start Agent
        Start-Sleep -Seconds 10
        Start-Service -Name $agentService
        Write-Log "Started SQL Server Agent: $agentService"
        
        return $true
    }
    catch {
        Write-Log "Error starting SQL services: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Function to backup databases
function Backup-SQLDatabases {
    param([string]$InstanceName)
    
    try {
        Write-Log "Starting database backup for instance: $InstanceName"
        
        $serverName = if ($InstanceName -eq "DEFAULT") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$InstanceName" }
        $backupPath = "C:\temp\SQLBackups\$InstanceName"
        
        if (!(Test-Path $backupPath)) {
            New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        }
        
        # Use SQLCMD to backup system databases
        $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
        
        if ($sqlcmdPath) {
            $backupScript = @"
BACKUP DATABASE [master] TO DISK = '$backupPath\master_backup.bak' WITH INIT;
BACKUP DATABASE [model] TO DISK = '$backupPath\model_backup.bak' WITH INIT;
BACKUP DATABASE [msdb] TO DISK = '$backupPath\msdb_backup.bak' WITH INIT;
"@
            
            $backupScript | Out-File -FilePath "$backupPath\backup_script.sql" -Force
            
            & $sqlcmdPath.FullName -S $serverName -E -i "$backupPath\backup_script.sql"
            Write-Log "Database backup completed for instance: $InstanceName"
            return $true
        }
        else {
            Write-Log "SQLCMD not found, skipping backup" -Level "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Error during backup: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Function to download SQL Server update
function Get-SQLServerUpdate {
    param([string]$Version)
    
    $downloadUrls = @{
        "2022" = "https://download.microsoft.com/download/3/8/d/38de7036-2433-4207-8eae-06e247e17b25/SQLServer2022-KB5046057-x64.exe"
        "2019" = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5046057-x64.exe"
        "2017" = "https://download.microsoft.com/download/c/4/f/c4f908c9-98ed-4e5f-88d5-7a6ae7b5c0d8/SQLServer2017-KB5046057-x64.exe"
    }
    
    $url = $downloadUrls[$Version]
    if (!$url) {
        Write-Log "No download URL available for SQL Server $Version" -Level "ERROR"
        return $null
    }
    
    $downloadPath = "C:\temp\SQLServerUpdate_$Version.exe"
    
    try {
        Write-Log "Downloading SQL Server $Version update from: $url"
        
        # Create temp directory if it doesn't exist
        if (!(Test-Path "C:\temp")) {
            New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
        }
        
        # Download the update
        Invoke-WebRequest -Uri $url -OutFile $downloadPath -UseBasicParsing
        
        if (Test-Path $downloadPath) {
            Write-Log "Download completed: $downloadPath"
            return $downloadPath
        }
        else {
            Write-Log "Download failed: File not found at $downloadPath" -Level "ERROR"
            return $null
        }
    }
    catch {
        Write-Log "Error downloading update: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

# Function to install SQL Server update
function Install-SQLServerUpdate {
    param(
        [string]$UpdatePath,
        [string]$InstanceName
    )
    
    try {
        Write-Log "Installing SQL Server update for instance: $InstanceName"
        Write-Log "Update package: $UpdatePath"
        
        # Prepare installation arguments
        $installArgs = @(
            "/quiet",
            "/IAcceptSQLServerLicenseTerms",
            "/Action=Patch",
            "/AllInstances"
        )
        
        if ($InstanceName -ne "DEFAULT") {
            $installArgs += "/InstanceName=$InstanceName"
        }
        
        # Start the installation
        $process = Start-Process -FilePath $UpdatePath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "SQL Server update installed successfully for instance: $InstanceName"
            return $true
        }
        else {
            Write-Log "SQL Server update failed with exit code: $($process.ExitCode)" -Level "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Error installing update: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# Main execution starts here
Write-Log "Starting SQL Server Update Process"

# Check admin rights
if (!(Test-AdminRights)) {
    Write-Log "This script requires administrator privileges" -Level "ERROR"
    exit 1
}

# Create log directory
$logDir = Split-Path $LogPath -Parent
if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
}

# Detect current SQL Server instances
Write-Log "Detecting SQL Server instances..."
$detectionScript = Join-Path $PSScriptRoot "Detect-SQLServerVersion.ps1"

if (!(Test-Path $detectionScript)) {
    Write-Log "Detection script not found: $detectionScript" -Level "ERROR"
    exit 1
}

# Run detection
& $detectionScript -ExportToCSV

# Read the compliance results
$complianceFile = "C:\temp\SQLServerCompliance.json"
if (!(Test-Path $complianceFile)) {
    Write-Log "No SQL Server instances found to update" -Level "WARNING"
    exit 0
}

$complianceData = Get-Content $complianceFile | ConvertFrom-Json
$instances = $complianceData.Instances

Write-Log "Found $($instances.Count) SQL Server instance(s) to evaluate for updates"

foreach ($instance in $instances) {
    Write-Log "Processing instance: $($instance.InstanceName)"
    Write-Log "Current version: $($instance.Version)"
    Write-Log "Product: $($instance.ProductName)"
    
    # Determine if update is needed
    $needsUpdate = $false
    $currentMajorVersion = $instance.Version.Split('.')[0]
    
    switch ($TargetVersion) {
        "2022" { $needsUpdate = [int]$currentMajorVersion -lt 16 }
        "2019" { $needsUpdate = [int]$currentMajorVersion -lt 15 }
        "2017" { $needsUpdate = [int]$currentMajorVersion -lt 14 }
        default { 
            Write-Log "Unsupported target version: $TargetVersion" -Level "ERROR"
            continue
        }
    }
    
    if (!$needsUpdate -and !$ForceUpdate) {
        Write-Log "Instance $($instance.InstanceName) is already at or above target version"
        continue
    }
    
    Write-Log "Instance $($instance.InstanceName) requires update to SQL Server $TargetVersion"
    
    # Backup databases if requested
    if ($BackupFirst) {
        if (!(Backup-SQLDatabases -InstanceName $instance.InstanceName)) {
            Write-Log "Backup failed for instance $($instance.InstanceName), skipping update" -Level "ERROR"
            continue
        }
    }
    
    # Download update package if not provided
    if (!$UpdatePackagePath) {
        $UpdatePackagePath = Get-SQLServerUpdate -Version $TargetVersion
        if (!$UpdatePackagePath) {
            Write-Log "Failed to download update package" -Level "ERROR"
            continue
        }
    }
    
    # Stop SQL services
    if (!(Stop-SQLServices -InstanceName $instance.InstanceName)) {
        Write-Log "Failed to stop SQL services for instance $($instance.InstanceName)" -Level "ERROR"
        continue
    }
    
    # Install update
    if (Install-SQLServerUpdate -UpdatePath $UpdatePackagePath -InstanceName $instance.InstanceName) {
        Write-Log "Update completed successfully for instance: $($instance.InstanceName)"
        
        # Start services
        Start-SQLServices -InstanceName $instance.InstanceName
        
        Write-Log "Instance $($instance.InstanceName) update process completed"
    }
    else {
        Write-Log "Update failed for instance: $($instance.InstanceName)" -Level "ERROR"
        
        # Try to start services anyway
        Start-SQLServices -InstanceName $instance.InstanceName
    }
}

Write-Log "SQL Server update process completed"

# Run detection again to verify updates
Write-Log "Running post-update detection..."
& $detectionScript -ExportToCSV

Write-Log "Update process finished. Check log file: $LogPath"
