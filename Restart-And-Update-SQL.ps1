# Restart Computer and Schedule SQL Server Update
# This script handles the restart and schedules the SQL update to run after reboot

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER",
    
    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst
)

Write-Host "SQL Server Update - Restart and Schedule Script" -ForegroundColor Green
Write-Host "=" * 50

# Check if running as administrator
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (!$isAdmin) {
    Write-Host "ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Get current script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$updateScript = Join-Path $scriptDir "SQL 2019_Update-Script-test.ps1"

if (!(Test-Path $updateScript)) {
    Write-Host "ERROR: SQL update script not found at: $updateScript" -ForegroundColor Red
    exit 1
}

Write-Host "Found SQL update script: $updateScript" -ForegroundColor Green

# Create a batch file to run after restart
$batchFile = "C:\temp\RunSQLUpdate.bat"
$batchContent = @"
@echo off
echo Starting SQL Server Update after restart...
cd /d "$scriptDir"
powershell.exe -ExecutionPolicy Bypass -File "$updateScript" -InstanceName "$InstanceName" $(if($BackupFirst){"-BackupFirst"})
pause
"@

# Ensure temp directory exists
if (!(Test-Path "C:\temp")) {
    New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
}

# Write batch file
$batchContent | Out-File -FilePath $batchFile -Encoding ASCII -Force
Write-Host "Created post-restart batch file: $batchFile" -ForegroundColor Green

# Create registry entry to run after restart
$runOnceKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
$runOnceName = "SQLServerUpdate"

try {
    Set-ItemProperty -Path $runOnceKey -Name $runOnceName -Value $batchFile -Force
    Write-Host "Scheduled SQL Server update to run after restart" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to schedule post-restart task: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Function to show notification popup
function Show-RestartNotification {
    param(
        [string]$InstanceName,
        [bool]$BackupFirst
    )

    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.Drawing

    # Create the main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "SQL Server 2019 Update - Restart Required"
    $form.Size = New-Object System.Drawing.Size(500, 400)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.MinimizeBox = $false
    $form.TopMost = $true
    $form.Icon = [System.Drawing.SystemIcons]::Warning

    # Create warning icon
    $iconLabel = New-Object System.Windows.Forms.Label
    $iconLabel.Location = New-Object System.Drawing.Point(20, 20)
    $iconLabel.Size = New-Object System.Drawing.Size(32, 32)
    $iconLabel.Image = [System.Drawing.SystemIcons]::Warning.ToBitmap()
    $form.Controls.Add($iconLabel)

    # Create title label
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Location = New-Object System.Drawing.Point(70, 20)
    $titleLabel.Size = New-Object System.Drawing.Size(400, 30)
    $titleLabel.Text = "System Restart Required for SQL Server Update"
    $titleLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 12, [System.Drawing.FontStyle]::Bold)
    $titleLabel.ForeColor = [System.Drawing.Color]::DarkRed
    $form.Controls.Add($titleLabel)

    # Create message text
    $messageText = @"
A system restart is required before SQL Server 2019 can be updated.

CURRENT SITUATION:
• SQL Server Version: 15.0.2000.5 (RTM)
• Target Version: 15.0.4430.1 (CU32)
• System Status: Pending reboot detected

WHAT WILL HAPPEN AFTER RESTART:
1. Computer will restart
2. SQL Server update will run automatically
3. Update will install SQL Server 2019 CU32 (887 MB)
4. SQL Server services will be managed automatically
$(if($BackupFirst){"5. Database backup will be performed before update"})

ESTIMATED TIME: 15-30 minutes total
DOWNTIME: SQL Server will be briefly unavailable during update

Choose when to restart:
"@

    $messageLabel = New-Object System.Windows.Forms.Label
    $messageLabel.Location = New-Object System.Drawing.Point(20, 60)
    $messageLabel.Size = New-Object System.Drawing.Size(450, 220)
    $messageLabel.Text = $messageText
    $messageLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9)
    $form.Controls.Add($messageLabel)

    # Create buttons
    $restartNowButton = New-Object System.Windows.Forms.Button
    $restartNowButton.Location = New-Object System.Drawing.Point(50, 300)
    $restartNowButton.Size = New-Object System.Drawing.Size(120, 35)
    $restartNowButton.Text = "Restart Now"
    $restartNowButton.BackColor = [System.Drawing.Color]::LightCoral
    $restartNowButton.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9, [System.Drawing.FontStyle]::Bold)
    $restartNowButton.DialogResult = [System.Windows.Forms.DialogResult]::Yes
    $form.Controls.Add($restartNowButton)

    $restartLaterButton = New-Object System.Windows.Forms.Button
    $restartLaterButton.Location = New-Object System.Drawing.Point(190, 300)
    $restartLaterButton.Size = New-Object System.Drawing.Size(120, 35)
    $restartLaterButton.Text = "Restart in 5 min"
    $restartLaterButton.BackColor = [System.Drawing.Color]::LightBlue
    $restartLaterButton.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9)
    $restartLaterButton.DialogResult = [System.Windows.Forms.DialogResult]::Retry
    $form.Controls.Add($restartLaterButton)

    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Location = New-Object System.Drawing.Point(330, 300)
    $cancelButton.Size = New-Object System.Drawing.Size(120, 35)
    $cancelButton.Text = "Cancel"
    $cancelButton.BackColor = [System.Drawing.Color]::LightGray
    $cancelButton.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9)
    $cancelButton.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
    $form.Controls.Add($cancelButton)

    # Set default button
    $form.AcceptButton = $restartNowButton
    $form.CancelButton = $cancelButton

    # Show the form and return result
    return $form.ShowDialog()
}

# Show notification popup
Write-Host "Showing restart notification popup..." -ForegroundColor Cyan
$userChoice = Show-RestartNotification -InstanceName $InstanceName -BackupFirst $BackupFirst

switch ($userChoice) {
    "Yes" {
        Write-Host "User selected: Restart Now" -ForegroundColor Green
        Write-Host "Restarting computer in 5 seconds..." -ForegroundColor Red

        # Show countdown
        for ($i = 5; $i -gt 0; $i--) {
            Write-Host "Restarting in $i seconds..." -ForegroundColor Yellow
            Start-Sleep -Seconds 1
        }

        Write-Host "Restarting now..." -ForegroundColor Red
        Restart-Computer -Force
    }

    "Retry" {
        Write-Host "User selected: Restart in 5 minutes" -ForegroundColor Yellow
        Write-Host "System will restart in 5 minutes..." -ForegroundColor Yellow

        # Show 5-minute countdown with periodic updates
        $totalSeconds = 300  # 5 minutes
        $updateInterval = 60  # Update every minute

        for ($remaining = $totalSeconds; $remaining -gt 0; $remaining -= $updateInterval) {
            $minutes = [math]::Ceiling($remaining / 60)
            Write-Host "Restart scheduled in $minutes minute(s)..." -ForegroundColor Yellow

            if ($remaining -le $updateInterval) {
                Start-Sleep -Seconds $remaining
                break
            } else {
                Start-Sleep -Seconds $updateInterval
            }
        }

        Write-Host "5 minutes elapsed. Restarting now..." -ForegroundColor Red
        Restart-Computer -Force
    }

    "Cancel" {
        Write-Host "User cancelled restart." -ForegroundColor Yellow
        Write-Host "SQL Server update has been cancelled." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "IMPORTANT: The update cannot proceed until the system is restarted." -ForegroundColor Red
        Write-Host "Please restart manually when ready and run the update script again." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "To run update after manual restart:" -ForegroundColor Cyan
        Write-Host ".\SQL 2019_Update-Script-test.ps1 -BackupFirst" -ForegroundColor White

        # Remove the scheduled task since user cancelled
        try {
            Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce" -Name "SQLServerUpdate" -ErrorAction SilentlyContinue
            Remove-Item -Path "C:\temp\RunSQLUpdate.bat" -Force -ErrorAction SilentlyContinue
            Write-Host "Removed scheduled update task." -ForegroundColor Green
        } catch {
            Write-Host "Note: You may need to manually remove scheduled task if it was created." -ForegroundColor Yellow
        }

        exit 0
    }

    default {
        Write-Host "Dialog was closed. Cancelling restart." -ForegroundColor Yellow
        exit 0
    }
}
