# Restart Computer and Schedule SQL Server Update
# This script handles the restart and schedules the SQL update to run after reboot

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER",
    
    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst
)

Write-Host "SQL Server Update - Restart and Schedule Script" -ForegroundColor Green
Write-Host "=" * 50

# Check if running as administrator
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (!$isAdmin) {
    Write-Host "ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Get current script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$updateScript = Join-Path $scriptDir "SQL 2019_Update-Script-test.ps1"

if (!(Test-Path $updateScript)) {
    Write-Host "ERROR: SQL update script not found at: $updateScript" -ForegroundColor Red
    exit 1
}

Write-Host "Found SQL update script: $updateScript" -ForegroundColor Green

# Create a batch file to run after restart
$batchFile = "C:\temp\RunSQLUpdate.bat"
$batchContent = @"
@echo off
echo Starting SQL Server Update after restart...
cd /d "$scriptDir"
powershell.exe -ExecutionPolicy Bypass -File "$updateScript" -InstanceName "$InstanceName" $(if($BackupFirst){"-BackupFirst"})
pause
"@

# Ensure temp directory exists
if (!(Test-Path "C:\temp")) {
    New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
}

# Write batch file
$batchContent | Out-File -FilePath $batchFile -Encoding ASCII -Force
Write-Host "Created post-restart batch file: $batchFile" -ForegroundColor Green

# Create registry entry to run after restart
$runOnceKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
$runOnceName = "SQLServerUpdate"

try {
    Set-ItemProperty -Path $runOnceKey -Name $runOnceName -Value $batchFile -Force
    Write-Host "Scheduled SQL Server update to run after restart" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to schedule post-restart task: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nREADY TO RESTART!" -ForegroundColor Yellow
Write-Host "=" * 50
Write-Host "What will happen:" -ForegroundColor Cyan
Write-Host "1. Computer will restart now" -ForegroundColor White
Write-Host "2. After restart, SQL Server update will run automatically" -ForegroundColor White
Write-Host "3. A command window will open showing the update progress" -ForegroundColor White
Write-Host "4. Update will install SQL Server 2019 CU32" -ForegroundColor White
if ($BackupFirst) {
    Write-Host "5. Database backup will be performed before update" -ForegroundColor White
}

Write-Host "`nPress any key to restart now, or Ctrl+C to cancel..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

Write-Host "`nRestarting computer in 5 seconds..." -ForegroundColor Red
Start-Sleep -Seconds 5

# Restart the computer
Restart-Computer -Force
