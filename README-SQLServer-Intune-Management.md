# SQL Server Version Management in Microsoft Intune

This solution provides comprehensive SQL Server version detection, compliance checking, and automated updates for devices managed by Microsoft Intune.

## Overview

The solution consists of three main PowerShell scripts:

1. **Detect-SQLServerVersion.ps1** - Detects all SQL Server instances and their versions
2. **Intune-SQLServer-Compliance.ps1** - Checks compliance against organizational policies
3. **Update-SQLServer.ps1** - Automates SQL Server updates

## Features

- **Multi-Method Detection**: Uses both registry and WMI/CIM for comprehensive SQL Server discovery
- **Version Identification**: Accurately identifies SQL Server versions from 2000 to 2022
- **Compliance Checking**: Validates against minimum version requirements and security support status
- **Automated Updates**: Downloads and installs SQL Server updates with proper service management
- **Backup Integration**: Optional database backup before updates
- **Intune Integration**: Designed for deployment through Microsoft Intune
- **Comprehensive Logging**: Detailed logging for troubleshooting and audit purposes

## Script Details

### 1. Detect-SQLServerVersion.ps1

**Purpose**: Discovers all SQL Server instances on the device and reports their versions.

**Key Features**:
- Registry-based detection for accurate version information
- WMI service detection as fallback method
- Supports all SQL Server versions from 2000 to 2022
- Exports results to CSV and JSON formats
- Creates compliance data for Intune processing

**Usage**:
```powershell
# Basic detection
.\Detect-SQLServerVersion.ps1

# Export to CSV
.\Detect-SQLServerVersion.ps1 -ExportToCSV

# Custom output path
.\Detect-SQLServerVersion.ps1 -ExportToCSV -OutputPath "C:\Reports\SQLInventory.csv"
```

**Output Files**:
- `C:\temp\SQLServerInventory.csv` - Detailed instance information
- `C:\temp\SQLServerCompliance.json` - Compliance data for Intune

### 2. Intune-SQLServer-Compliance.ps1

**Purpose**: Evaluates SQL Server instances against compliance policies.

**Key Features**:
- Configurable minimum version requirements
- Security support status checking
- Approved version list validation
- Detailed compliance reporting
- Intune-compatible exit codes

**Usage**:
```powershell
# Check against SQL Server 2017 minimum
.\Intune-SQLServer-Compliance.ps1 -MinimumVersion "2017"

# Check with security update requirements
.\Intune-SQLServer-Compliance.ps1 -MinimumVersion "2017" -CheckSecurityUpdates

# Specify allowed versions
.\Intune-SQLServer-Compliance.ps1 -RequiredVersions @("2019", "2022")
```

**Compliance Criteria**:
- Version meets minimum requirements
- Version is in approved list
- Version receives security updates (if enabled)

### 3. Update-SQLServer.ps1

**Purpose**: Automates SQL Server updates to newer versions.

**Key Features**:
- Automatic update package download
- Service management (stop/start)
- Optional database backup
- Support for multiple instances
- Comprehensive error handling and logging

**Usage**:
```powershell
# Update to SQL Server 2022
.\Update-SQLServer.ps1 -TargetVersion "2022"

# Update with backup
.\Update-SQLServer.ps1 -TargetVersion "2022" -BackupFirst

# Force update regardless of current version
.\Update-SQLServer.ps1 -TargetVersion "2022" -ForceUpdate

# Use custom update package
.\Update-SQLServer.ps1 -TargetVersion "2022" -UpdatePackagePath "C:\Updates\SQLServer2022-Update.exe"
```

## Intune Deployment

### Step 1: Create Win32 App Package

1. **Package the Scripts**:
   - Create a folder with all PowerShell scripts
   - Use the Microsoft Win32 Content Prep Tool to create `.intunewin` package
   
2. **Install Command**:
   ```cmd
   powershell.exe -ExecutionPolicy Bypass -File "Detect-SQLServerVersion.ps1"
   ```

3. **Detection Rule**:
   - Use custom script detection
   - Script: `Intune-SQLServer-Compliance.ps1`
   - Detection logic: Script returns "COMPLIANT"

### Step 2: Configure Compliance Policy

1. **Create Custom Compliance Policy**:
   - Platform: Windows 10 and later
   - Use custom compliance script: `Intune-SQLServer-Compliance.ps1`

2. **Compliance Settings**:
   ```powershell
   # Example compliance script call
   .\Intune-SQLServer-Compliance.ps1 -MinimumVersion "2017" -CheckSecurityUpdates
   ```

### Step 3: Create Remediation Script

1. **Detection Script**: `Intune-SQLServer-Compliance.ps1`
2. **Remediation Script**: `Update-SQLServer.ps1`

**Remediation Command**:
```powershell
powershell.exe -ExecutionPolicy Bypass -File "Update-SQLServer.ps1" -TargetVersion "2022" -BackupFirst
```

### Step 4: Assignment and Monitoring

1. **Assign to Device Groups**:
   - Target appropriate device groups
   - Set deployment schedule (e.g., maintenance windows)

2. **Monitor Deployment**:
   - Use Intune reporting to track compliance
   - Review device compliance status
   - Monitor update success/failure rates

## Configuration Options

### Minimum Version Requirements

Modify the compliance script to set your organization's minimum SQL Server version:

```powershell
# Require SQL Server 2017 or later
$MinimumVersion = "2017"

# Require SQL Server 2019 or later
$MinimumVersion = "2019"
```

### Approved Versions List

Specify which SQL Server versions are approved in your environment:

```powershell
$RequiredVersions = @("2017", "2019", "2022")  # Allow these versions only
$RequiredVersions = @("2022")                   # Only allow SQL Server 2022
```

### Security Update Checking

Enable checking for versions that no longer receive security updates:

```powershell
.\Intune-SQLServer-Compliance.ps1 -CheckSecurityUpdates
```

## Troubleshooting

### Common Issues

1. **Script Execution Policy**:
   - Ensure PowerShell execution policy allows script execution
   - Use `-ExecutionPolicy Bypass` parameter

2. **Permissions**:
   - Scripts require local administrator privileges
   - Ensure Intune runs scripts in system context

3. **Detection Failures**:
   - Check if SQL Server is installed via non-standard methods
   - Verify registry keys exist and are accessible

4. **Update Failures**:
   - Ensure sufficient disk space for updates
   - Check network connectivity for downloads
   - Verify SQL Server services can be stopped/started

### Log Files

- **Detection Log**: Console output and JSON files in `C:\temp\`
- **Update Log**: `C:\temp\SQLServerUpdate.log`
- **Compliance Log**: `C:\temp\SQLServerComplianceResults.json`

### Registry Locations

SQL Server installations are detected in these registry locations:
- `HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server`
- `HKLM:\SOFTWARE\WOW6432Node\Microsoft\Microsoft SQL Server`

## Security Considerations

1. **Script Signing**: Consider code signing scripts for enhanced security
2. **Network Access**: Update downloads require internet connectivity
3. **Service Accounts**: Ensure proper service account permissions
4. **Backup Strategy**: Always backup databases before major updates

## Support Matrix

| SQL Server Version | Detection | Compliance | Updates |
|-------------------|-----------|------------|---------|
| SQL Server 2022   | ✅        | ✅         | ✅      |
| SQL Server 2019   | ✅        | ✅         | ✅      |
| SQL Server 2017   | ✅        | ✅         | ✅      |
| SQL Server 2016   | ✅        | ✅         | ⚠️      |
| SQL Server 2014   | ✅        | ✅         | ⚠️      |
| SQL Server 2012   | ✅        | ⚠️         | ❌      |
| SQL Server 2008 R2| ✅        | ❌         | ❌      |
| SQL Server 2008   | ✅        | ❌         | ❌      |

✅ Fully Supported | ⚠️ Limited Support | ❌ Not Supported

## Best Practices

1. **Test in Development**: Always test scripts in a development environment first
2. **Staged Rollout**: Deploy to pilot groups before organization-wide deployment
3. **Maintenance Windows**: Schedule updates during maintenance windows
4. **Backup Strategy**: Implement comprehensive backup strategy before updates
5. **Monitoring**: Set up monitoring and alerting for compliance status
6. **Documentation**: Maintain documentation of approved versions and policies

## Version History

- **v1.0**: Initial release with basic detection and compliance
- **v1.1**: Added update automation and backup functionality
- **v1.2**: Enhanced error handling and logging
- **v1.3**: Improved Intune integration and reporting
