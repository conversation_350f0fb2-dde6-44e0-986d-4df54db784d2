# SQL Server Version Detection Script for Intune
# This script detects all SQL Server instances and their versions on the device

param(
    [switch]$ExportToCSV,
    [string]$OutputPath = "C:\temp\SQLServerInventory.csv"
)

# Function to get SQL Server version information
function Get-SQLServerVersionInfo {
    param($VersionNumber)
    
    $versionMap = @{
        "16" = "SQL Server 2022"
        "15" = "SQL Server 2019"
        "14" = "SQL Server 2017"
        "13" = "SQL Server 2016"
        "12" = "SQL Server 2014"
        "11" = "SQL Server 2012"
        "10.5" = "SQL Server 2008 R2"
        "10" = "SQL Server 2008"
        "9" = "SQL Server 2005"
        "8" = "SQL Server 2000"
    }
    
    $majorVersion = $VersionNumber.Split('.')[0]
    if ($VersionNumber.StartsWith("10.5")) { $majorVersion = "10.5" }
    
    return $versionMap[$majorVersion] ?? "Unknown SQL Server Version"
}

# Function to check if SQL Server is installed via registry
function Get-SQLServerFromRegistry {
    $sqlInstances = @()
    
    try {
        # Check for SQL Server installations in registry
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Microsoft SQL Server"
        )
        
        foreach ($regPath in $registryPaths) {
            if (Test-Path $regPath) {
                $installedInstances = Get-ItemProperty -Path "$regPath" -Name "InstalledInstances" -ErrorAction SilentlyContinue
                
                if ($installedInstances) {
                    foreach ($instance in $installedInstances.InstalledInstances) {
                        try {
                            $instancePath = "$regPath\Instance Names\SQL"
                            if (Test-Path $instancePath) {
                                $instanceKey = Get-ItemProperty -Path $instancePath -Name $instance -ErrorAction SilentlyContinue
                                if ($instanceKey) {
                                    $setupPath = "$regPath\$($instanceKey.$instance)\Setup"
                                    if (Test-Path $setupPath) {
                                        $setupInfo = Get-ItemProperty -Path $setupPath -ErrorAction SilentlyContinue
                                        
                                        $sqlInstance = [PSCustomObject]@{
                                            InstanceName = $instance
                                            Version = $setupInfo.Version ?? "Unknown"
                                            Edition = $setupInfo.Edition ?? "Unknown"
                                            ProductName = Get-SQLServerVersionInfo -VersionNumber ($setupInfo.Version ?? "0")
                                            InstallPath = $setupInfo.SQLPath ?? "Unknown"
                                            ServicePack = $setupInfo.SP ?? "0"
                                            PatchLevel = $setupInfo.PatchLevel ?? "Unknown"
                                            Collation = $setupInfo.Collation ?? "Unknown"
                                            DetectionMethod = "Registry"
                                        }
                                        $sqlInstances += $sqlInstance
                                    }
                                }
                            }
                        }
                        catch {
                            Write-Warning "Error processing instance $instance: $($_.Exception.Message)"
                        }
                    }
                }
            }
        }
    }
    catch {
        Write-Error "Error accessing registry: $($_.Exception.Message)"
    }
    
    return $sqlInstances
}

# Function to check SQL Server via WMI/CIM
function Get-SQLServerFromWMI {
    $sqlInstances = @()
    
    try {
        # Get SQL Server services
        $sqlServices = Get-CimInstance -ClassName Win32_Service | Where-Object { 
            $_.Name -like "MSSQL*" -or $_.Name -like "SQLServer*" 
        }
        
        foreach ($service in $sqlServices) {
            try {
                $instanceName = if ($service.Name -eq "MSSQLSERVER") { "DEFAULT" } else { $service.Name -replace "MSSQL\$", "" }
                
                # Try to get version from executable
                $version = "Unknown"
                $productName = "Unknown"
                
                if ($service.PathName) {
                    $exePath = ($service.PathName -split '"')[1]
                    if ($exePath -and (Test-Path $exePath)) {
                        $fileVersion = (Get-ItemProperty $exePath).VersionInfo.FileVersion
                        if ($fileVersion) {
                            $version = $fileVersion
                            $productName = Get-SQLServerVersionInfo -VersionNumber $fileVersion
                        }
                    }
                }
                
                $sqlInstance = [PSCustomObject]@{
                    InstanceName = $instanceName
                    Version = $version
                    Edition = "Unknown"
                    ProductName = $productName
                    InstallPath = $service.PathName
                    ServicePack = "Unknown"
                    PatchLevel = "Unknown"
                    Collation = "Unknown"
                    DetectionMethod = "WMI Service"
                    ServiceName = $service.Name
                    ServiceState = $service.State
                    ServiceStartMode = $service.StartMode
                }
                $sqlInstances += $sqlInstance
            }
            catch {
                Write-Warning "Error processing service $($service.Name): $($_.Exception.Message)"
            }
        }
    }
    catch {
        Write-Error "Error accessing WMI: $($_.Exception.Message)"
    }
    
    return $sqlInstances
}

# Main execution
Write-Host "Starting SQL Server Detection..." -ForegroundColor Green

# Combine results from both methods
$allInstances = @()
$allInstances += Get-SQLServerFromRegistry
$allInstances += Get-SQLServerFromWMI

# Remove duplicates based on instance name
$uniqueInstances = $allInstances | Sort-Object InstanceName | Group-Object InstanceName | ForEach-Object {
    # Prefer registry detection over WMI when available
    $_.Group | Sort-Object DetectionMethod | Select-Object -First 1
}

if ($uniqueInstances.Count -eq 0) {
    Write-Host "No SQL Server instances found on this device." -ForegroundColor Yellow
    exit 0
}

# Display results
Write-Host "`nSQL Server Instances Found:" -ForegroundColor Green
Write-Host "=" * 50

foreach ($instance in $uniqueInstances) {
    Write-Host "`nInstance: $($instance.InstanceName)" -ForegroundColor Cyan
    Write-Host "Product: $($instance.ProductName)"
    Write-Host "Version: $($instance.Version)"
    Write-Host "Edition: $($instance.Edition)"
    Write-Host "Install Path: $($instance.InstallPath)"
    Write-Host "Service Pack: $($instance.ServicePack)"
    Write-Host "Detection Method: $($instance.DetectionMethod)"
    
    if ($instance.ServiceState) {
        Write-Host "Service State: $($instance.ServiceState)"
        Write-Host "Start Mode: $($instance.ServiceStartMode)"
    }
}

# Export to CSV if requested
if ($ExportToCSV) {
    try {
        $outputDir = Split-Path $OutputPath -Parent
        if (!(Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }
        
        $uniqueInstances | Export-Csv -Path $OutputPath -NoTypeInformation -Force
        Write-Host "`nResults exported to: $OutputPath" -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to export CSV: $($_.Exception.Message)"
    }
}

# Create compliance output for Intune
$complianceResult = @{
    DeviceName = $env:COMPUTERNAME
    Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    SQLInstancesFound = $uniqueInstances.Count
    Instances = $uniqueInstances
}

# Output JSON for Intune processing
$complianceResult | ConvertTo-Json -Depth 3 | Out-File -FilePath "C:\temp\SQLServerCompliance.json" -Force

Write-Host "`nDetection complete. Found $($uniqueInstances.Count) SQL Server instance(s)." -ForegroundColor Green
