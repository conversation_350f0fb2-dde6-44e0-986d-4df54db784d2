# SQL Server 2019 Update - Intune Deployment Guide

## 📦 Package Preparation

### Step 1: Create Package Directory
Create a folder structure like this:
```
SQLServer2019Update/
├── Deploy-SQLUpdate-Intune.ps1     (Main installation script)
├── Detect-SQLUpdate-Intune.ps1     (Detection script)
└── install.cmd                     (Wrapper batch file)
```

### Step 2: Create install.cmd
Create a batch file named `install.cmd` with this content:
```batch
@echo off
powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -File "%~dp0Deploy-SQLUpdate-Intune.ps1" -BackupFirst
exit /b %ERRORLEVEL%
```

### Step 3: Package with Win32 Content Prep Tool
1. Download Microsoft Win32 Content Prep Tool
2. Run: `IntuneWinAppUtil.exe -c "C:\SQLServer2019Update" -s "install.cmd" -o "C:\Output"`
3. This creates `install.intunewin` file

## 🚀 Intune Configuration

### Win32 App Settings

#### **Basic Information**
- **Name**: SQL Server 2019 CU32 Update
- **Description**: Updates SQL Server 2019 to Cumulative Update 32 (15.0.4430.1)
- **Publisher**: Your Organization
- **Category**: Productivity

#### **Program Settings**
- **Install command**: `install.cmd`
- **Uninstall command**: `echo "Uninstall not applicable"`
- **Install behavior**: System
- **Device restart behavior**: Determine behavior based on return codes

#### **Requirements**
- **Operating system architecture**: x64
- **Minimum operating system**: Windows 10 1607
- **Additional requirements**: 
  - PowerShell 5.0 or later
  - 3 GB free disk space

#### **Detection Rules**
Choose **Custom detection script**:
- **Script file**: Upload `Detect-SQLUpdate-Intune.ps1`
- **Run script as 32-bit process**: No

#### **Return Codes**
| Return Code | Code Type | Description |
|-------------|-----------|-------------|
| 0 | Success | Installation successful |
| 1603 | Hard reboot | Installation failed |
| 1618 | Soft reboot | Pending reboot detected - retry after reboot |
| 3010 | Soft reboot | Installation successful - reboot required |

#### **Assignments**
- **Required**: Assign to device groups with SQL Server 2019
- **Available**: Optional - for user-initiated updates
- **Deadline**: Set appropriate deadline for your environment

## 🎯 Alternative Deployment Methods

### Option 2: PowerShell Script Deployment

#### Create PowerShell Script in Intune:
1. Go to **Devices** > **Scripts** > **Add** > **Windows 10 and later**
2. Upload: `Deploy-SQLUpdate-Intune.ps1`
3. Configure:
   - **Run this script using the logged on credentials**: No
   - **Enforce script signature check**: No
   - **Run script in 64-bit PowerShell**: Yes

#### Assignment:
- Assign to device groups with SQL Server 2019
- Set appropriate schedule

### Option 3: Remediation Scripts

#### Detection Script:
Upload `Detect-SQLUpdate-Intune.ps1` as detection script

#### Remediation Script:
Upload `Deploy-SQLUpdate-Intune.ps1` as remediation script

#### Configuration:
- **Run this script using the logged on credentials**: No
- **Enforce script signature check**: No
- **Run script in 64-bit PowerShell**: Yes
- **Schedule**: Daily or weekly

## 📊 Monitoring and Reporting

### Device Status Monitoring
Monitor deployment through:
- **Apps** > **SQL Server 2019 CU32 Update** > **Device install status**
- **Devices** > **Monitor** > **App installations**

### Log File Locations
- **Intune Management Extension**: `C:\ProgramData\Microsoft\IntuneManagementExtension\Logs\`
- **SQL Update Log**: `C:\ProgramData\Microsoft\IntuneManagementExtension\Logs\SQLServer2019Update.log`

### Success Criteria
- Exit code 0 or 3010
- Detection script returns "INSTALLED"
- SQL Server version shows 15.0.4430.1

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Issue: "Pending reboot detected"
- **Cause**: System needs restart before update
- **Solution**: Script returns 1618, Intune will retry after reboot
- **Action**: Ensure devices restart regularly

#### Issue: "SQL Server not found"
- **Cause**: Device doesn't have SQL Server 2019
- **Solution**: Script exits with code 0 (not applicable)
- **Action**: Review assignment targeting

#### Issue: "Download failed"
- **Cause**: Network connectivity or firewall
- **Solution**: Check internet access and firewall rules
- **Action**: Ensure devices can reach download.microsoft.com

#### Issue: "Installation failed with exit code -2042429437"
- **Cause**: System state issues or pending reboot
- **Solution**: Script handles this automatically
- **Action**: Wait for retry after reboot

### Firewall Requirements
Ensure devices can access:
- `download.microsoft.com` (port 443)
- `www.microsoft.com` (port 443)

## 📋 Pre-Deployment Checklist

- [ ] Test package on pilot devices
- [ ] Verify detection script works correctly
- [ ] Confirm network connectivity to Microsoft downloads
- [ ] Set appropriate maintenance windows
- [ ] Notify users of potential SQL Server downtime
- [ ] Backup critical databases before deployment
- [ ] Monitor first wave deployment before broader rollout

## 🎯 Deployment Strategy

### Phase 1: Pilot (Week 1)
- Deploy to 5-10 test devices
- Monitor for 48 hours
- Verify successful updates

### Phase 2: Limited Production (Week 2)
- Deploy to 25% of SQL Server devices
- Monitor for issues
- Gather feedback

### Phase 3: Full Deployment (Week 3-4)
- Deploy to remaining devices
- Stagger deployment over time
- Monitor completion rates

## 📞 Support Information

### For End Users:
- SQL Server will be briefly unavailable during update
- No user action required
- Contact IT if SQL Server applications don't work after update

### For IT Support:
- Check Intune deployment status first
- Review log files for detailed error information
- Verify SQL Server version post-update
- Restart SQL Server services if needed

## 🔄 Post-Deployment

### Verification Steps:
1. Check Intune deployment success rate
2. Verify SQL Server version on updated devices
3. Test SQL Server functionality
4. Monitor for any application issues
5. Update documentation with lessons learned

### Cleanup:
- Remove old update files from devices (handled automatically)
- Archive deployment logs
- Update inventory with new SQL Server versions
