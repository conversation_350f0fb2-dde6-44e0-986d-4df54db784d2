# Test SQL Server 2019 CU32 Download URL
# This script tests if the download URL is working

Write-Host "Testing SQL Server 2019 CU32 Download URL..." -ForegroundColor Green
Write-Host "=" * 60

# CU32 Information
$updateInfo = @{
    Version = "15.0.4430.1"
    KB = "KB5054833"
    DirectDownloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5054833-x64.exe"
    Description = "Cumulative Update 32 for SQL Server 2019"
}

Write-Host "Update Information:" -ForegroundColor Cyan
Write-Host "  Description: $($updateInfo.Description)" -ForegroundColor White
Write-Host "  Version: $($updateInfo.Version)" -ForegroundColor White
Write-Host "  KB: $($updateInfo.KB)" -ForegroundColor White
Write-Host "  Download URL: $($updateInfo.DirectDownloadUrl)" -ForegroundColor White

Write-Host "`nTesting download URL accessibility..." -ForegroundColor Yellow

try {
    # Test if URL is accessible (HEAD request)
    $response = Invoke-WebRequest -Uri $updateInfo.DirectDownloadUrl -Method Head -UseBasicParsing -TimeoutSec 30
    
    Write-Host "✅ URL Test: SUCCESS" -ForegroundColor Green
    Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Status Description: $($response.StatusDescription)" -ForegroundColor White
    
    # Get content length if available
    $contentLength = $response.Headers['Content-Length']
    if ($contentLength) {
        $sizeMB = [math]::Round([int64]$contentLength / 1MB, 2)
        Write-Host "   File Size: $sizeMB MB" -ForegroundColor White
    }
    
    # Get content type
    $contentType = $response.Headers['Content-Type']
    if ($contentType) {
        Write-Host "   Content Type: $contentType" -ForegroundColor White
    }
    
    Write-Host "`n✅ DOWNLOAD URL IS WORKING!" -ForegroundColor Green
    Write-Host "The SQL Server 2019 CU32 download should work correctly." -ForegroundColor Green
    
} catch {
    Write-Host "❌ URL Test: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "   Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        Write-Host "   Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
    }
    
    Write-Host "`n❌ DOWNLOAD URL IS NOT WORKING!" -ForegroundColor Red
    Write-Host "You will need to manually download the update." -ForegroundColor Red
    Write-Host "Manual download page: https://www.microsoft.com/en-us/download/details.aspx?id=100809" -ForegroundColor Yellow
}

Write-Host "`n" + "=" * 60
Write-Host "Test completed!" -ForegroundColor Green
