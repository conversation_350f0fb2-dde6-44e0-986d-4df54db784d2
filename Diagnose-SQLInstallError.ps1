# SQL Server Installation Error Diagnosis
# This script helps diagnose the installation error -2042429437

Write-Host "SQL Server Installation Error Diagnosis" -ForegroundColor Red
Write-Host "=" * 50

# Convert the error code
$errorCode = -2042429437
$hexCode = [Convert]::ToString($errorCode, 16)
$unsignedCode = [uint32]$errorCode

Write-Host "Error Code Analysis:" -ForegroundColor Yellow
Write-Host "  Decimal: $errorCode" -ForegroundColor White
Write-Host "  Hex: 0x$hexCode" -ForegroundColor White
Write-Host "  Unsigned: $unsignedCode" -ForegroundColor White

# Common SQL Server error codes
$sqlErrorCodes = @{
    "-2042429437" = "SQL Server setup failed. This typically indicates a problem with installation parameters or system requirements."
    "-2068052991" = "Another installation is in progress"
    "-2068643839" = "Installation package is corrupt or incompatible"
    "-2068578303" = "Insufficient privileges or blocked by antivirus"
    "1603" = "Fatal error during installation"
    "1619" = "Installation package could not be opened"
    "1638" = "Another version of this product is already installed"
}

if ($sqlErrorCodes.ContainsKey($errorCode.ToString())) {
    Write-Host "`nKnown Error:" -ForegroundColor Red
    Write-Host "  $($sqlErrorCodes[$errorCode.ToString()])" -ForegroundColor White
} else {
    Write-Host "`nUnknown Error Code" -ForegroundColor Yellow
}

Write-Host "`nCommon Causes and Solutions:" -ForegroundColor Cyan
Write-Host "1. PENDING REBOOT REQUIRED" -ForegroundColor Yellow
Write-Host "   - Check if system needs reboot before installation" -ForegroundColor White
Write-Host "   - Solution: Restart computer and try again" -ForegroundColor Green

Write-Host "`n2. ANTIVIRUS INTERFERENCE" -ForegroundColor Yellow
Write-Host "   - Real-time scanning may block installation" -ForegroundColor White
Write-Host "   - Solution: Temporarily disable antivirus during update" -ForegroundColor Green

Write-Host "`n3. INSUFFICIENT DISK SPACE" -ForegroundColor Yellow
Write-Host "   - SQL Server updates require significant free space" -ForegroundColor White
Write-Host "   - Solution: Free up disk space (need ~2-3 GB)" -ForegroundColor Green

Write-Host "`n4. CORRUPTED INSTALLATION FILES" -ForegroundColor Yellow
Write-Host "   - Downloaded file may be corrupted" -ForegroundColor White
Write-Host "   - Solution: Delete and re-download the update file" -ForegroundColor Green

Write-Host "`n5. WRONG INSTALLATION PARAMETERS" -ForegroundColor Yellow
Write-Host "   - Installation arguments may be incorrect" -ForegroundColor White
Write-Host "   - Solution: Use simpler installation parameters" -ForegroundColor Green

Write-Host "`n6. ACTIVE SQL SERVER CONNECTIONS" -ForegroundColor Yellow
Write-Host "   - Applications may still be connected to SQL Server" -ForegroundColor White
Write-Host "   - Solution: Stop all SQL Server dependent services first" -ForegroundColor Green

# Check system state
Write-Host "`n" + "=" * 50
Write-Host "SYSTEM STATE CHECK" -ForegroundColor Cyan
Write-Host "=" * 50

# Check pending reboot
Write-Host "`nChecking for pending reboot..." -ForegroundColor Yellow
$pendingReboot = $false

# Check registry for pending reboot
$regKeys = @(
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired",
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Component Based Servicing\RebootPending",
    "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager"
)

foreach ($key in $regKeys) {
    if (Test-Path $key) {
        if ($key -like "*Session Manager*") {
            $sessionManager = Get-ItemProperty -Path $key -ErrorAction SilentlyContinue
            if ($sessionManager.PendingFileRenameOperations) {
                $pendingReboot = $true
                break
            }
        } else {
            $pendingReboot = $true
            break
        }
    }
}

if ($pendingReboot) {
    Write-Host "WARNING: System has pending reboot!" -ForegroundColor Red
    Write-Host "SOLUTION: Restart computer before running SQL Server update" -ForegroundColor Green
} else {
    Write-Host "OK: No pending reboot detected" -ForegroundColor Green
}

# Check disk space
Write-Host "`nChecking disk space..." -ForegroundColor Yellow
$systemDrive = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "C:" }
if ($systemDrive) {
    $freeSpaceGB = [math]::Round($systemDrive.FreeSpace / 1GB, 2)
    Write-Host "C: drive free space: $freeSpaceGB GB" -ForegroundColor White
    
    if ($freeSpaceGB -lt 3) {
        Write-Host "WARNING: Low disk space! Need at least 3 GB free" -ForegroundColor Red
        Write-Host "SOLUTION: Free up disk space before installation" -ForegroundColor Green
    } else {
        Write-Host "OK: Sufficient disk space available" -ForegroundColor Green
    }
}

# Check SQL Server services
Write-Host "`nChecking SQL Server services..." -ForegroundColor Yellow
$sqlServices = Get-Service | Where-Object { $_.Name -like "*SQL*" }
foreach ($service in $sqlServices) {
    Write-Host "  $($service.Name): $($service.Status)" -ForegroundColor White
}

# Check for SQL Server processes
Write-Host "`nChecking for SQL Server processes..." -ForegroundColor Yellow
$sqlProcesses = Get-Process | Where-Object { $_.ProcessName -like "*sql*" }
if ($sqlProcesses) {
    Write-Host "Active SQL Server processes found:" -ForegroundColor Yellow
    foreach ($process in $sqlProcesses) {
        Write-Host "  $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor White
    }
    Write-Host "SOLUTION: Stop all SQL Server services before installation" -ForegroundColor Green
} else {
    Write-Host "OK: No active SQL Server processes" -ForegroundColor Green
}

Write-Host "`n" + "=" * 50
Write-Host "RECOMMENDED ACTIONS" -ForegroundColor Green
Write-Host "=" * 50

Write-Host "1. RESTART COMPUTER (if pending reboot detected)" -ForegroundColor Yellow
Write-Host "2. TEMPORARILY DISABLE ANTIVIRUS" -ForegroundColor Yellow
Write-Host "3. FREE UP DISK SPACE (if needed)" -ForegroundColor Yellow
Write-Host "4. USE SIMPLIFIED INSTALLATION PARAMETERS" -ForegroundColor Yellow
Write-Host "5. TRY MANUAL INSTALLATION AS FALLBACK" -ForegroundColor Yellow

Write-Host "`nDiagnosis completed!" -ForegroundColor Green
