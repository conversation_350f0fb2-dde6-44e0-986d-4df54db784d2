# Test Service Management Functions
# This script tests the service management functions without actually stopping services

Write-Host "Testing SQL Server Service Management Functions..." -ForegroundColor Green
Write-Host "=" * 60

# Function to get service names based on instance
function Get-ServiceNames {
    param([string]$Instance)
    
    if ($Instance -eq "MSSQLSERVER") {
        return @{
            Engine = "MSSQLSERVER"
            Agent = "SQLSERVERAGENT"
        }
    } else {
        return @{
            Engine = "MSSQL`$$Instance"
            Agent = "SQLAgent`$$Instance"
        }
    }
}

# Function to wait for service to reach desired status
function Wait-ForServiceStatus {
    param(
        [string]$ServiceName,
        [string]$DesiredStatus,
        [int]$TimeoutSeconds = 60
    )
    
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    
    do {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq $DesiredStatus) {
            return $true
        }
        Start-Sleep -Seconds 2
    } while ((Get-Date) -lt $timeout)
    
    return $false
}

# Test service detection
Write-Host "Testing service name detection..." -ForegroundColor Cyan

$instanceName = "MSSQLSERVER"
$serviceNames = Get-ServiceNames -Instance $instanceName

Write-Host "Instance: $instanceName" -ForegroundColor White
Write-Host "  Engine Service: $($serviceNames.Engine)" -ForegroundColor White
Write-Host "  Agent Service: $($serviceNames.Agent)" -ForegroundColor White

# Check if services exist
Write-Host "`nChecking if SQL Server services exist..." -ForegroundColor Cyan

$engineService = Get-Service -Name $serviceNames.Engine -ErrorAction SilentlyContinue
$agentService = Get-Service -Name $serviceNames.Agent -ErrorAction SilentlyContinue

if ($engineService) {
    Write-Host "✅ SQL Server Engine service found" -ForegroundColor Green
    Write-Host "   Name: $($engineService.Name)" -ForegroundColor White
    Write-Host "   Status: $($engineService.Status)" -ForegroundColor White
    Write-Host "   Start Type: $($engineService.StartType)" -ForegroundColor White
} else {
    Write-Host "❌ SQL Server Engine service NOT found" -ForegroundColor Red
}

if ($agentService) {
    Write-Host "✅ SQL Server Agent service found" -ForegroundColor Green
    Write-Host "   Name: $($agentService.Name)" -ForegroundColor White
    Write-Host "   Status: $($agentService.Status)" -ForegroundColor White
    Write-Host "   Start Type: $($agentService.StartType)" -ForegroundColor White
} else {
    Write-Host "❌ SQL Server Agent service NOT found" -ForegroundColor Red
}

# Test the Wait-ForServiceStatus function
Write-Host "`nTesting Wait-ForServiceStatus function..." -ForegroundColor Cyan

if ($engineService) {
    Write-Host "Testing wait for current status of SQL Server Engine..." -ForegroundColor Yellow
    $currentStatus = $engineService.Status
    
    $startTime = Get-Date
    $result = Wait-ForServiceStatus -ServiceName $serviceNames.Engine -DesiredStatus $currentStatus -TimeoutSeconds 10
    $endTime = Get-Date
    $elapsed = ($endTime - $startTime).TotalSeconds
    
    if ($result) {
        Write-Host "✅ Wait function works correctly" -ForegroundColor Green
        Write-Host "   Waited for status: $currentStatus" -ForegroundColor White
        Write-Host "   Time elapsed: $([math]::Round($elapsed, 2)) seconds" -ForegroundColor White
    } else {
        Write-Host "❌ Wait function failed" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ Cannot test Wait-ForServiceStatus - SQL Server Engine service not found" -ForegroundColor Yellow
}

# Summary
Write-Host "`n" + "=" * 60
Write-Host "SERVICE MANAGEMENT TEST RESULTS:" -ForegroundColor Green
Write-Host "=" * 60

if ($engineService -and $agentService) {
    Write-Host "✅ ALL TESTS PASSED" -ForegroundColor Green
    Write-Host "   Service detection: Working" -ForegroundColor White
    Write-Host "   Service status checking: Working" -ForegroundColor White
    Write-Host "   Wait function: Working" -ForegroundColor White
    Write-Host "`n✅ The updated script should work correctly!" -ForegroundColor Green
} elseif ($engineService) {
    Write-Host "⚠️ PARTIAL SUCCESS" -ForegroundColor Yellow
    Write-Host "   SQL Server Engine: Found" -ForegroundColor White
    Write-Host "   SQL Server Agent: Not Found" -ForegroundColor White
    Write-Host "`n⚠️ The script will work but may show warnings about SQL Agent" -ForegroundColor Yellow
} else {
    Write-Host "❌ TESTS FAILED" -ForegroundColor Red
    Write-Host "   SQL Server services not found" -ForegroundColor White
    Write-Host "`n❌ Please verify SQL Server 2019 is installed and services are configured" -ForegroundColor Red
}

Write-Host "`n" + "=" * 60
Write-Host "Test completed!" -ForegroundColor Green
