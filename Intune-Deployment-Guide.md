# Microsoft Intune Deployment Guide for SQL Server Management

This guide provides step-by-step instructions for deploying SQL Server version management through Microsoft Intune.

## Prerequisites

- Microsoft Intune subscription
- Windows 10/11 devices enrolled in Intune
- Global Administrator or Intune Administrator permissions
- Microsoft Win32 Content Prep Tool
- PowerShell scripts from this solution

## Deployment Methods

### Method 1: Win32 App Deployment (Recommended)

#### Step 1: Prepare the Package

1. **Create Package Folder**:
   ```
   SQLServerManagement/
   ├── Detect-SQLServerVersion.ps1
   ├── Intune-SQLServer-Compliance.ps1
   ├── Update-SQLServer.ps1
   └── install.cmd
   ```

2. **Create install.cmd**:
   ```cmd
   @echo off
   REM SQL Server Detection and Compliance Check
   powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -File "%~dp0Detect-SQLServerVersion.ps1" -ExportToCSV
   
   REM Run compliance check
   powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -File "%~dp0Intune-SQLServer-Compliance.ps1" -MinimumVersion "2017" -CheckSecurityUpdates
   
   exit /b %ERRORLEVEL%
   ```

3. **Create .intunewin Package**:
   ```cmd
   IntuneWinAppUtil.exe -c "C:\SQLServerManagement" -s "install.cmd" -o "C:\IntunePackages"
   ```

#### Step 2: Create Win32 App in Intune

1. **Navigate to Intune Admin Center**:
   - Go to Apps > All apps > Add

2. **App Information**:
   - App type: Windows app (Win32)
   - Name: SQL Server Version Management
   - Description: Detects and manages SQL Server versions
   - Publisher: Your Organization

3. **Program Settings**:
   - Install command: `install.cmd`
   - Uninstall command: `cmd /c echo "No uninstall required"`
   - Install behavior: System
   - Device restart behavior: No specific action

4. **Requirements**:
   - Operating system architecture: x64
   - Minimum operating system: Windows 10 1607

5. **Detection Rules**:
   - Rules format: Use a custom detection script
   - Script file: Upload `Intune-SQLServer-Compliance.ps1`
   - Run script as 32-bit process: No

6. **Dependencies**: None

7. **Supersedence**: None

8. **Assignments**:
   - Required: Select device groups that need SQL Server management
   - Available for enrolled devices: Optional
   - Uninstall: Not applicable

#### Step 3: Monitor Deployment

1. **Check App Installation Status**:
   - Apps > All apps > SQL Server Version Management > Device install status

2. **Review Compliance**:
   - Monitor device compliance through Intune reporting
   - Check for failed installations and remediate

### Method 2: PowerShell Script Deployment

#### Step 1: Create PowerShell Scripts in Intune

1. **Navigate to Devices > Scripts**

2. **Add Detection Script**:
   - Name: SQL Server Detection
   - Script: Upload `Detect-SQLServerVersion.ps1`
   - Run this script using the logged on credentials: No
   - Enforce script signature check: No
   - Run script in 64-bit PowerShell Host: Yes

3. **Add Compliance Script**:
   - Name: SQL Server Compliance Check
   - Script: Upload `Intune-SQLServer-Compliance.ps1`
   - Configure script parameters as needed

#### Step 2: Assign Scripts

1. **Select Target Groups**:
   - Assign to device groups containing SQL Server instances
   - Set appropriate deployment schedule

2. **Monitor Script Execution**:
   - Check script run status in Intune
   - Review output and error logs

### Method 3: Compliance Policy with Remediation

#### Step 1: Create Custom Compliance Policy

1. **Navigate to Devices > Compliance policies**

2. **Create Policy**:
   - Platform: Windows 10 and later
   - Name: SQL Server Version Compliance

3. **Compliance Settings**:
   - Add custom compliance setting
   - Upload `Intune-SQLServer-Compliance.ps1`
   - Configure detection logic

4. **Actions for Noncompliance**:
   - Mark device noncompliant: Immediately
   - Send push notification: After 1 day
   - Remotely lock device: After 7 days (optional)

#### Step 2: Create Remediation Script

1. **Navigate to Devices > Remediations**

2. **Create Script Package**:
   - Name: SQL Server Update Remediation
   - Detection script: `Intune-SQLServer-Compliance.ps1`
   - Remediation script: `Update-SQLServer.ps1`

3. **Settings**:
   - Run this script using the logged on credentials: No
   - Enforce script signature check: No
   - Run script in 64-bit PowerShell Host: Yes

4. **Assignments**:
   - Assign to groups with non-compliant devices
   - Set run schedule (e.g., weekly during maintenance window)

## Configuration Examples

### Example 1: Basic SQL Server 2017+ Compliance

**Compliance Script Parameters**:
```powershell
# Require SQL Server 2017 or later
-MinimumVersion "2017"
```

**Detection Rule**:
- Script returns "COMPLIANT" for compliant devices
- Script returns "NON_COMPLIANT" for non-compliant devices

### Example 2: Strict SQL Server 2019+ with Security Updates

**Compliance Script Parameters**:
```powershell
# Require SQL Server 2019+ with security support
-MinimumVersion "2019" -CheckSecurityUpdates
```

**Remediation Script Parameters**:
```powershell
# Update to SQL Server 2022 with backup
-TargetVersion "2022" -BackupFirst
```

### Example 3: Approved Versions Only

**Compliance Script Parameters**:
```powershell
# Only allow SQL Server 2019 and 2022
-RequiredVersions @("2019", "2022") -CheckSecurityUpdates
```

## Advanced Configuration

### Custom Detection Logic

Create custom detection rules for specific scenarios:

```powershell
# Custom detection for specific SQL Server editions
$complianceScript = @"
param([string]$RequiredEdition = "Enterprise")

# Run detection
& "$PSScriptRoot\Detect-SQLServerVersion.ps1" -ExportToCSV

# Check for required edition
$complianceFile = "C:\temp\SQLServerCompliance.json"
if (Test-Path $complianceFile) {
    $data = Get-Content $complianceFile | ConvertFrom-Json
    $hasRequiredEdition = $data.Instances | Where-Object { $_.Edition -like "*$RequiredEdition*" }
    
    if ($hasRequiredEdition) {
        Write-Output "COMPLIANT"
        exit 0
    }
}

Write-Output "NON_COMPLIANT"
exit 1
"@
```

### Conditional Updates

Configure updates based on specific conditions:

```powershell
# Update only if specific conditions are met
$updateScript = @"
param(
    [string]$TargetVersion = "2022",
    [string]$MinCurrentVersion = "2016"
)

# Check current versions first
& "$PSScriptRoot\Detect-SQLServerVersion.ps1" -ExportToCSV

$complianceFile = "C:\temp\SQLServerCompliance.json"
if (Test-Path $complianceFile) {
    $data = Get-Content $complianceFile | ConvertFrom-Json
    
    # Only update instances that meet minimum version requirement
    $eligibleInstances = $data.Instances | Where-Object { 
        $_.Version -ge $MinCurrentVersion -and $_.Version -lt $TargetVersion 
    }
    
    if ($eligibleInstances.Count -gt 0) {
        & "$PSScriptRoot\Update-SQLServer.ps1" -TargetVersion $TargetVersion -BackupFirst
    }
}
"@
```

## Monitoring and Reporting

### Built-in Intune Reports

1. **App Installation Reports**:
   - Device install status
   - User install status
   - App installation failures

2. **Compliance Reports**:
   - Device compliance status
   - Compliance policy reports
   - Non-compliant device details

3. **Script Execution Reports**:
   - PowerShell script run status
   - Script output and errors
   - Device-level execution details

### Custom Reporting

Create custom reports using the JSON output files:

```powershell
# Aggregate compliance data from multiple devices
$reportScript = @"
# Collect compliance data from network locations
$devices = Get-ADComputer -Filter * | Select-Object -ExpandProperty Name
$complianceData = @()

foreach ($device in $devices) {
    $complianceFile = "\\$device\c$\temp\SQLServerCompliance.json"
    if (Test-Path $complianceFile) {
        $data = Get-Content $complianceFile | ConvertFrom-Json
        $complianceData += $data
    }
}

# Generate summary report
$summary = @{
    TotalDevices = $complianceData.Count
    CompliantDevices = ($complianceData | Where-Object { $_.ComplianceStatus -eq "COMPLIANT" }).Count
    NonCompliantDevices = ($complianceData | Where-Object { $_.ComplianceStatus -eq "NON_COMPLIANT" }).Count
    TotalSQLInstances = ($complianceData | Measure-Object -Property TotalInstances -Sum).Sum
}

$summary | ConvertTo-Json | Out-File "C:\Reports\SQLServerComplianceSummary.json"
"@
```

## Troubleshooting

### Common Deployment Issues

1. **Script Execution Failures**:
   - Check PowerShell execution policy
   - Verify script permissions
   - Review Intune agent logs

2. **Detection Rule Issues**:
   - Test detection scripts locally
   - Check script output format
   - Verify exit codes

3. **Update Failures**:
   - Check network connectivity
   - Verify sufficient disk space
   - Review SQL Server service status

### Diagnostic Commands

```powershell
# Check Intune Management Extension status
Get-Service -Name "Microsoft Intune Management Extension"

# Review Intune logs
Get-WinEvent -LogName "Microsoft-Windows-DeviceManagement-Enterprise-Diagnostics-Provider/Admin"

# Test script execution locally
powershell.exe -ExecutionPolicy Bypass -File "Detect-SQLServerVersion.ps1"
```

## Best Practices

1. **Pilot Testing**:
   - Test with small groups first
   - Validate scripts in lab environment
   - Monitor for unexpected issues

2. **Maintenance Windows**:
   - Schedule updates during maintenance windows
   - Coordinate with database administrators
   - Plan for potential downtime

3. **Backup Strategy**:
   - Always backup before updates
   - Test backup restoration procedures
   - Document rollback procedures

4. **Change Management**:
   - Follow organizational change management processes
   - Document all configuration changes
   - Maintain version control for scripts

5. **Security**:
   - Use least privilege principles
   - Consider script signing
   - Secure sensitive parameters

## Support and Maintenance

### Regular Tasks

1. **Update Scripts**:
   - Keep scripts updated with latest SQL Server versions
   - Update download URLs for patches
   - Refresh compliance requirements

2. **Monitor Compliance**:
   - Review compliance reports regularly
   - Address non-compliant devices promptly
   - Update policies as needed

3. **Performance Monitoring**:
   - Monitor script execution times
   - Check for resource usage issues
   - Optimize scripts as needed

### Escalation Procedures

1. **Script Failures**:
   - Check device-specific logs
   - Test scripts manually on affected devices
   - Engage PowerShell/SQL Server experts

2. **Compliance Issues**:
   - Review compliance policy settings
   - Check for environmental factors
   - Coordinate with security team

3. **Update Failures**:
   - Engage database administrators
   - Check SQL Server-specific logs
   - Plan manual remediation if needed
