# SQL Server 2019 Update Detection Script for Intune
# This script detects if SQL Server 2019 CU32 is installed

try {
    # Define target version (CU32)
    $targetVersion = "15.0.4430.1"
    
    # Check registry for SQL Server 2019 installation
    $registryPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup",
        "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQL2019\Setup",
        "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQLEXPRESS\Setup"
    )
    
    $sqlFound = $false
    $currentVersion = $null
    
    foreach ($regPath in $registryPaths) {
        if (Test-Path $regPath) {
            $setupInfo = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
            if ($setupInfo -and $setupInfo.Version) {
                $sqlFound = $true
                $currentVersion = $setupInfo.Version
                break
            }
        }
    }
    
    if (!$sqlFound) {
        # SQL Server 2019 not found - app not applicable
        Write-Output "SQL Server 2019 not found - not applicable"
        exit 0
    }
    
    # Compare versions
    $current = [Version]$currentVersion
    $target = [Version]$targetVersion
    
    if ($current -ge $target) {
        # Update is installed (current version is equal or greater than target)
        Write-Output "INSTALLED: SQL Server 2019 version $currentVersion (target: $targetVersion)"
        exit 0
    } else {
        # Update is needed
        Write-Output "NOT_INSTALLED: SQL Server 2019 version $currentVersion needs update to $targetVersion"
        exit 1
    }
    
} catch {
    # Error in detection - assume not installed
    Write-Output "ERROR: Detection failed - $($_.Exception.Message)"
    exit 1
}
