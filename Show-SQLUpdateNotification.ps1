# SQL Server Update Notification Script
# Shows a professional notification popup before SQL Server update

param(
    [Parameter(Mandatory=$false)]
    [string]$Title = "SQL Server 2019 Update Required",
    
    [Parameter(Mandatory=$false)]
    [string]$Message = "A SQL Server update is ready to install.",
    
    [Parameter(Mandatory=$false)]
    [int]$TimeoutSeconds = 0  # 0 = no timeout
)

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

function Show-UpdateNotification {
    param(
        [string]$Title,
        [string]$Message,
        [int]$TimeoutSeconds
    )
    
    # Create the main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = $Title
    $form.Size = New-Object System.Drawing.Size(550, 350)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.MinimizeBox = $false
    $form.TopMost = $true
    $form.BackColor = [System.Drawing.Color]::White
    
    # Create header panel
    $headerPanel = New-Object System.Windows.Forms.Panel
    $headerPanel.Location = New-Object System.Drawing.Point(0, 0)
    $headerPanel.Size = New-Object System.Drawing.Size(550, 60)
    $headerPanel.BackColor = [System.Drawing.Color]::DarkBlue
    $form.Controls.Add($headerPanel)
    
    # Create header icon
    $headerIcon = New-Object System.Windows.Forms.Label
    $headerIcon.Location = New-Object System.Drawing.Point(15, 15)
    $headerIcon.Size = New-Object System.Drawing.Size(32, 32)
    $headerIcon.Image = [System.Drawing.SystemIcons]::Information.ToBitmap()
    $headerPanel.Controls.Add($headerIcon)
    
    # Create header title
    $headerTitle = New-Object System.Windows.Forms.Label
    $headerTitle.Location = New-Object System.Drawing.Point(60, 20)
    $headerTitle.Size = New-Object System.Drawing.Size(450, 25)
    $headerTitle.Text = "SQL Server 2019 Update Manager"
    $headerTitle.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 14, [System.Drawing.FontStyle]::Bold)
    $headerTitle.ForeColor = [System.Drawing.Color]::White
    $headerPanel.Controls.Add($headerTitle)
    
    # Create main message
    $messageLabel = New-Object System.Windows.Forms.Label
    $messageLabel.Location = New-Object System.Drawing.Point(30, 80)
    $messageLabel.Size = New-Object System.Drawing.Size(480, 120)
    $messageLabel.Text = @"
SQL Server 2019 Update Available

Current Version: 15.0.2000.5 (RTM)
Available Update: 15.0.4430.1 (Cumulative Update 32)

This update includes:
• Security fixes and performance improvements
• Bug fixes and stability enhancements
• Latest compatibility updates

System restart is required before installation can proceed.
The update process will take approximately 15-30 minutes.
"@
    $messageLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 10)
    $messageLabel.ForeColor = [System.Drawing.Color]::Black
    $form.Controls.Add($messageLabel)
    
    # Create warning box
    $warningPanel = New-Object System.Windows.Forms.Panel
    $warningPanel.Location = New-Object System.Drawing.Point(30, 210)
    $warningPanel.Size = New-Object System.Drawing.Size(480, 50)
    $warningPanel.BackColor = [System.Drawing.Color]::LightYellow
    $warningPanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
    $form.Controls.Add($warningPanel)
    
    $warningIcon = New-Object System.Windows.Forms.Label
    $warningIcon.Location = New-Object System.Drawing.Point(10, 10)
    $warningIcon.Size = New-Object System.Drawing.Size(16, 16)
    $warningIcon.Image = [System.Drawing.SystemIcons]::Warning.ToBitmap()
    $warningPanel.Controls.Add($warningIcon)
    
    $warningText = New-Object System.Windows.Forms.Label
    $warningText.Location = New-Object System.Drawing.Point(35, 5)
    $warningText.Size = New-Object System.Drawing.Size(430, 40)
    $warningText.Text = "SQL Server services will be temporarily unavailable during the update process. Please ensure no critical operations are running."
    $warningText.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9)
    $warningText.ForeColor = [System.Drawing.Color]::DarkOrange
    $warningPanel.Controls.Add($warningText)
    
    # Create buttons
    $proceedButton = New-Object System.Windows.Forms.Button
    $proceedButton.Location = New-Object System.Drawing.Point(150, 280)
    $proceedButton.Size = New-Object System.Drawing.Size(100, 30)
    $proceedButton.Text = "Proceed"
    $proceedButton.BackColor = [System.Drawing.Color]::LightGreen
    $proceedButton.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9, [System.Drawing.FontStyle]::Bold)
    $proceedButton.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $form.Controls.Add($proceedButton)
    
    $postponeButton = New-Object System.Windows.Forms.Button
    $postponeButton.Location = New-Object System.Drawing.Point(270, 280)
    $postponeButton.Size = New-Object System.Drawing.Size(100, 30)
    $postponeButton.Text = "Postpone"
    $postponeButton.BackColor = [System.Drawing.Color]::LightBlue
    $postponeButton.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 9)
    $postponeButton.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
    $form.Controls.Add($postponeButton)
    
    # Add timeout functionality if specified
    if ($TimeoutSeconds -gt 0) {
        $timer = New-Object System.Windows.Forms.Timer
        $timer.Interval = 1000  # 1 second
        $timeLeft = $TimeoutSeconds
        
        $timeoutLabel = New-Object System.Windows.Forms.Label
        $timeoutLabel.Location = New-Object System.Drawing.Point(30, 285)
        $timeoutLabel.Size = New-Object System.Drawing.Size(100, 20)
        $timeoutLabel.Text = "Auto-proceed in: $timeLeft"
        $timeoutLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 8)
        $timeoutLabel.ForeColor = [System.Drawing.Color]::Gray
        $form.Controls.Add($timeoutLabel)
        
        $timer.Add_Tick({
            $script:timeLeft--
            $timeoutLabel.Text = "Auto-proceed in: $script:timeLeft"
            
            if ($script:timeLeft -le 0) {
                $timer.Stop()
                $form.DialogResult = [System.Windows.Forms.DialogResult]::OK
                $form.Close()
            }
        })
        
        $timer.Start()
    }
    
    # Set default button
    $form.AcceptButton = $proceedButton
    $form.CancelButton = $postponeButton
    
    # Show the form and return result
    return $form.ShowDialog()
}

# Show the notification
Write-Host "Displaying SQL Server update notification..." -ForegroundColor Cyan

$result = Show-UpdateNotification -Title $Title -Message $Message -TimeoutSeconds $TimeoutSeconds

switch ($result) {
    "OK" {
        Write-Host "User selected: Proceed with update" -ForegroundColor Green
        exit 0
    }
    "Cancel" {
        Write-Host "User selected: Postpone update" -ForegroundColor Yellow
        exit 1
    }
    default {
        Write-Host "Dialog was closed or timed out" -ForegroundColor Yellow
        exit 1
    }
}
