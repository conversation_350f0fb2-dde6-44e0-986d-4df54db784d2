# SQL Server 2019 Update with User Notifications
# This script provides user-friendly notifications before performing the update

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER",
    
    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipNotifications
)

# Check if running as administrator
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (!$isAdmin) {
    Write-Host "ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and 'Run as Administrator'" -ForegroundColor Yellow
    
    if (!$SkipNotifications) {
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.MessageBox]::Show(
            "This script requires administrator privileges.`n`nPlease right-click PowerShell and select 'Run as Administrator'",
            "Administrator Rights Required",
            [System.Windows.Forms.MessageBoxButtons]::OK,
            [System.Windows.Forms.MessageBoxIcon]::Warning
        )
    }
    exit 1
}

Write-Host "SQL Server 2019 Update Manager" -ForegroundColor Green
Write-Host "=" * 40

# Get current script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$updateScript = Join-Path $scriptDir "SQL 2019_Update-Script-test.ps1"
$restartScript = Join-Path $scriptDir "Restart-And-Update-SQL.ps1"

# Check if required scripts exist
if (!(Test-Path $updateScript)) {
    Write-Host "ERROR: SQL update script not found at: $updateScript" -ForegroundColor Red
    exit 1
}

# Step 1: Show initial notification (unless skipped)
if (!$SkipNotifications) {
    Write-Host "Showing initial update notification..." -ForegroundColor Cyan
    
    Add-Type -AssemblyName System.Windows.Forms
    $result = [System.Windows.Forms.MessageBox]::Show(
        "SQL Server 2019 Update Available`n`n" +
        "Current Version: 15.0.2000.5 (RTM)`n" +
        "Available Update: 15.0.4430.1 (CU32)`n`n" +
        "This update includes security fixes, performance improvements, and bug fixes.`n`n" +
        "Do you want to proceed with the update?",
        "SQL Server 2019 Update Available",
        [System.Windows.Forms.MessageBoxButtons]::YesNo,
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::No) {
        Write-Host "User declined to proceed with update." -ForegroundColor Yellow
        exit 0
    }
    
    Write-Host "User confirmed to proceed with update." -ForegroundColor Green
}

# Step 2: Check for pending reboot
Write-Host "Checking system state..." -ForegroundColor Cyan

$pendingReboot = $false
$regKeys = @(
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired",
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Component Based Servicing\RebootPending",
    "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager"
)

foreach ($key in $regKeys) {
    if (Test-Path $key) {
        if ($key -like "*Session Manager*") {
            $sessionManager = Get-ItemProperty -Path $key -ErrorAction SilentlyContinue
            if ($sessionManager.PendingFileRenameOperations) {
                $pendingReboot = $true
                break
            }
        } else {
            $pendingReboot = $true
            break
        }
    }
}

# Step 3: Handle pending reboot or proceed with direct update
if ($pendingReboot) {
    Write-Host "PENDING REBOOT DETECTED - Restart required before update" -ForegroundColor Yellow
    
    if (!$SkipNotifications) {
        $restartResult = [System.Windows.Forms.MessageBox]::Show(
            "System Restart Required`n`n" +
            "A pending reboot has been detected. The SQL Server update cannot proceed until the system is restarted.`n`n" +
            "Would you like to restart now and automatically run the update after restart?`n`n" +
            "Click 'Yes' to restart now`n" +
            "Click 'No' to restart manually later",
            "Restart Required",
            [System.Windows.Forms.MessageBoxButtons]::YesNoCancel,
            [System.Windows.Forms.MessageBoxIcon]::Warning
        )
        
        switch ($restartResult) {
            "Yes" {
                Write-Host "User chose to restart now with automatic update" -ForegroundColor Green
                if (Test-Path $restartScript) {
                    & $restartScript -InstanceName $InstanceName $(if($BackupFirst){"-BackupFirst"})
                } else {
                    Write-Host "Restart script not found. Please restart manually." -ForegroundColor Red
                }
            }
            "No" {
                Write-Host "User chose to restart manually later" -ForegroundColor Yellow
                [System.Windows.Forms.MessageBox]::Show(
                    "Please restart your computer manually when convenient.`n`n" +
                    "After restart, run this script again to complete the SQL Server update.",
                    "Manual Restart Required",
                    [System.Windows.Forms.MessageBoxButtons]::OK,
                    [System.Windows.Forms.MessageBoxIcon]::Information
                )
            }
            "Cancel" {
                Write-Host "User cancelled the update process" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "Restart required. Run with restart script for automatic handling." -ForegroundColor Yellow
    }
    
    exit 0
    
} else {
    Write-Host "No pending reboot detected - proceeding with direct update" -ForegroundColor Green
    
    # Step 4: Final confirmation before update
    if (!$SkipNotifications) {
        $finalResult = [System.Windows.Forms.MessageBox]::Show(
            "Ready to Install SQL Server Update`n`n" +
            "The system is ready for the SQL Server 2019 update.`n`n" +
            "What will happen:`n" +
            "• Download SQL Server 2019 CU32 (887 MB)`n" +
            "• Stop SQL Server services temporarily`n" +
            "• Install the update silently`n" +
            "• Restart SQL Server services`n" +
            $(if($BackupFirst){"• Perform database backup before update`n"}) +
            "`nEstimated time: 15-30 minutes`n`n" +
            "Proceed with the update now?",
            "Final Confirmation",
            [System.Windows.Forms.MessageBoxButtons]::YesNo,
            [System.Windows.Forms.MessageBoxIcon]::Question
        )
        
        if ($finalResult -eq [System.Windows.Forms.DialogResult]::No) {
            Write-Host "User cancelled at final confirmation." -ForegroundColor Yellow
            exit 0
        }
    }
    
    # Step 5: Run the actual update
    Write-Host "Starting SQL Server 2019 update process..." -ForegroundColor Green
    Write-Host "Please wait while the update is performed..." -ForegroundColor Yellow
    
    try {
        $arguments = "-InstanceName `"$InstanceName`""
        if ($BackupFirst) {
            $arguments += " -BackupFirst"
        }
        
        # Run the update script
        $process = Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File `"$updateScript`" $arguments" -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host "SQL Server update completed successfully!" -ForegroundColor Green
            
            if (!$SkipNotifications) {
                [System.Windows.Forms.MessageBox]::Show(
                    "SQL Server Update Completed Successfully!`n`n" +
                    "SQL Server 2019 has been updated to CU32 (15.0.4430.1).`n`n" +
                    "All services have been restarted and are ready for use.",
                    "Update Successful",
                    [System.Windows.Forms.MessageBoxButtons]::OK,
                    [System.Windows.Forms.MessageBoxIcon]::Information
                )
            }
        } else {
            Write-Host "SQL Server update failed with exit code: $($process.ExitCode)" -ForegroundColor Red
            
            if (!$SkipNotifications) {
                [System.Windows.Forms.MessageBox]::Show(
                    "SQL Server Update Failed`n`n" +
                    "The update process encountered an error.`n" +
                    "Exit Code: $($process.ExitCode)`n`n" +
                    "Please check the log file for details:`n" +
                    "C:\temp\SQLServer2019Update.log",
                    "Update Failed",
                    [System.Windows.Forms.MessageBoxButtons]::OK,
                    [System.Windows.Forms.MessageBoxIcon]::Error
                )
            }
        }
        
    } catch {
        Write-Host "Error running update script: $($_.Exception.Message)" -ForegroundColor Red
        
        if (!$SkipNotifications) {
            [System.Windows.Forms.MessageBox]::Show(
                "Error Running Update`n`n" +
                "Failed to execute the update script.`n`n" +
                "Error: $($_.Exception.Message)",
                "Execution Error",
                [System.Windows.Forms.MessageBoxButtons]::OK,
                [System.Windows.Forms.MessageBoxIcon]::Error
            )
        }
    }
}

Write-Host "SQL Server Update Manager completed." -ForegroundColor Green
