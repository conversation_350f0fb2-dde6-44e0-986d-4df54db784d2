# Simple SQL Server 2019 Update Script Test
# This script tests the key components without special characters

Write-Host "SQL Server 2019 Update Script - Component Test" -ForegroundColor Green
Write-Host "=" * 50

# Test 1: Version Detection
Write-Host "`nTest 1: SQL Server Version Detection" -ForegroundColor Cyan

$registryPath = "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup"
if (Test-Path $registryPath) {
    $setupInfo = Get-ItemProperty -Path $registryPath -ErrorAction SilentlyContinue
    if ($setupInfo -and $setupInfo.Version) {
        Write-Host "SUCCESS: Found SQL Server version $($setupInfo.Version)" -ForegroundColor Green
        Write-Host "Edition: $($setupInfo.Edition)" -ForegroundColor White
        $currentVersion = $setupInfo.Version
    } else {
        Write-Host "FAILED: No version info in registry" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "FAILED: Registry path not found" -ForegroundColor Red
    exit 1
}

# Test 2: Update Check
Write-Host "`nTest 2: Update Availability Check" -ForegroundColor Cyan

$latestVersion = "15.0.4430.1"  # CU32
$needsUpdate = [Version]$currentVersion -lt [Version]$latestVersion

if ($needsUpdate) {
    Write-Host "SUCCESS: Update needed from $currentVersion to $latestVersion" -ForegroundColor Green
} else {
    Write-Host "INFO: Already up to date ($currentVersion)" -ForegroundColor Yellow
}

# Test 3: Service Detection
Write-Host "`nTest 3: SQL Server Service Detection" -ForegroundColor Cyan

$engineService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
$agentService = Get-Service -Name "SQLSERVERAGENT" -ErrorAction SilentlyContinue

if ($engineService) {
    Write-Host "SUCCESS: SQL Server Engine found - Status: $($engineService.Status)" -ForegroundColor Green
} else {
    Write-Host "FAILED: SQL Server Engine service not found" -ForegroundColor Red
}

if ($agentService) {
    Write-Host "SUCCESS: SQL Server Agent found - Status: $($agentService.Status)" -ForegroundColor Green
} else {
    Write-Host "WARNING: SQL Server Agent service not found" -ForegroundColor Yellow
}

# Test 4: Download URL Test
Write-Host "`nTest 4: Download URL Test" -ForegroundColor Cyan

$downloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5054833-x64.exe"

try {
    Write-Host "Testing URL accessibility..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri $downloadUrl -Method Head -UseBasicParsing -TimeoutSec 30
    Write-Host "SUCCESS: Download URL is accessible" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor White
    
    $contentLength = $response.Headers['Content-Length']
    if ($contentLength) {
        $sizeMB = [math]::Round([int64]$contentLength / 1MB, 2)
        Write-Host "File Size: $sizeMB MB" -ForegroundColor White
    }
} catch {
    Write-Host "FAILED: Download URL not accessible" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: PowerShell Compatibility
Write-Host "`nTest 5: PowerShell Compatibility" -ForegroundColor Cyan

Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White

# Test our custom Wait function
function Wait-ForServiceStatus {
    param(
        [string]$ServiceName,
        [string]$DesiredStatus,
        [int]$TimeoutSeconds = 10
    )
    
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    
    do {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq $DesiredStatus) {
            return $true
        }
        Start-Sleep -Seconds 1
    } while ((Get-Date) -lt $timeout)
    
    return $false
}

if ($engineService) {
    $currentStatus = $engineService.Status
    $result = Wait-ForServiceStatus -ServiceName "MSSQLSERVER" -DesiredStatus $currentStatus -TimeoutSeconds 5
    if ($result) {
        Write-Host "SUCCESS: Custom Wait-ForServiceStatus function works" -ForegroundColor Green
    } else {
        Write-Host "FAILED: Custom Wait-ForServiceStatus function failed" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n" + "=" * 50
Write-Host "TEST SUMMARY" -ForegroundColor Green
Write-Host "=" * 50

$allGood = $true

if (!$currentVersion) { $allGood = $false }
if (!$engineService) { $allGood = $false }

if ($allGood) {
    Write-Host "ALL TESTS PASSED!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your system is ready for SQL Server 2019 update:" -ForegroundColor White
    Write-Host "  Current Version: $currentVersion" -ForegroundColor White
    Write-Host "  Target Version: $latestVersion (CU32)" -ForegroundColor White
    Write-Host "  Update Needed: $needsUpdate" -ForegroundColor White
    Write-Host ""
    Write-Host "To run the actual update:" -ForegroundColor Yellow
    Write-Host "1. Right-click PowerShell and 'Run as Administrator'" -ForegroundColor Yellow
    Write-Host "2. Run: .\SQL 2019_Update-Script-test.ps1 -BackupFirst" -ForegroundColor Yellow
} else {
    Write-Host "SOME TESTS FAILED - Please resolve issues first" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
