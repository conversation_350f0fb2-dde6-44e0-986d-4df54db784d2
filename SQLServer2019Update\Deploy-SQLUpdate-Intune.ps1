# SQL Server 2019 Update - Intune Deployment Version
# This script is optimized for silent deployment through Intune

param(
    [Parameter(Mandatory=$false)]
    [string]$InstanceName = "MSSQLSERVER",
    
    [Parameter(Mandatory=$false)]
    [switch]$BackupFirst,
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\ProgramData\Microsoft\IntuneManagementExtension\Logs\SQLServer2019Update.log"
)

# Function to write log entries (Intune-compatible)
function Write-IntuneLog {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Write to console (for Intune logs)
    Write-Output $logEntry
    
    # Write to file
    try {
        $logDir = Split-Path $LogPath -Parent
        if (!(Test-Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        }
        Add-Content -Path $LogPath -Value $logEntry -Force
    } catch {
        Write-Output "Failed to write to log file: $($_.Exception.Message)"
    }
}

# Function to check if running as SYSTEM (Intune context)
function Test-SystemContext {
    $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
    return $currentUser.Name -eq "NT AUTHORITY\SYSTEM"
}

# Function to check for pending reboot
function Test-PendingReboot {
    try {
        Write-IntuneLog "Checking for pending reboot..."
        
        $pendingReboot = $false
        
        $regKeys = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired",
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Component Based Servicing\RebootPending",
            "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager"
        )
        
        foreach ($key in $regKeys) {
            if (Test-Path $key) {
                if ($key -like "*Session Manager*") {
                    $sessionManager = Get-ItemProperty -Path $key -ErrorAction SilentlyContinue
                    if ($sessionManager.PendingFileRenameOperations) {
                        $pendingReboot = $true
                        break
                    }
                } else {
                    $pendingReboot = $true
                    break
                }
            }
        }
        
        if ($pendingReboot) {
            Write-IntuneLog "PENDING REBOOT DETECTED - Update will be deferred" -Level "WARNING"
            return $true
        } else {
            Write-IntuneLog "No pending reboot detected - proceeding with update"
            return $false
        }
        
    } catch {
        Write-IntuneLog "Error checking for pending reboot: $($_.Exception.Message)" -Level "WARNING"
        return $false
    }
}

# Function to detect SQL Server version
function Get-CurrentSQLVersion {
    param([string]$Instance)
    
    try {
        Write-IntuneLog "Detecting SQL Server version for instance: $Instance"
        
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup",
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQL2019\Setup",
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQLEXPRESS\Setup"
        )
        
        foreach ($regPath in $registryPaths) {
            if (Test-Path $regPath) {
                $setupInfo = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
                if ($setupInfo -and $setupInfo.Version) {
                    Write-IntuneLog "Found SQL Server version: $($setupInfo.Version)"
                    return @{
                        Version = $setupInfo.Version
                        Edition = if ($setupInfo.Edition) { $setupInfo.Edition } else { "Unknown" }
                        DetectionMethod = "Registry ($regPath)"
                    }
                }
            }
        }
        
        Write-IntuneLog "SQL Server 2019 not found" -Level "WARNING"
        return $null
        
    } catch {
        Write-IntuneLog "Error detecting SQL Server version: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

# Function to check if update is needed
function Test-UpdateRequired {
    param([string]$CurrentVersion)
    
    $latestVersion = "15.0.4430.1"  # CU32
    
    try {
        $current = [Version]$CurrentVersion
        $latest = [Version]$latestVersion
        
        $needsUpdate = $current -lt $latest
        
        Write-IntuneLog "Version comparison: Current=$CurrentVersion, Latest=$latestVersion, UpdateNeeded=$needsUpdate"
        
        return $needsUpdate
        
    } catch {
        Write-IntuneLog "Error comparing versions: $($_.Exception.Message)" -Level "WARNING"
        return $true  # Assume update needed if comparison fails
    }
}

# Main execution
Write-IntuneLog "=== SQL Server 2019 Update - Intune Deployment Started ==="
Write-IntuneLog "Instance: $InstanceName"
Write-IntuneLog "Backup First: $BackupFirst"
Write-IntuneLog "Running as SYSTEM: $(Test-SystemContext)"

# Check for pending reboot first
if (Test-PendingReboot) {
    Write-IntuneLog "DEFERRING UPDATE: System restart required" -Level "ERROR"
    Write-IntuneLog "Intune will retry this deployment after the next reboot" -Level "INFO"
    exit 1618  # ERROR_INSTALL_PACKAGE_REJECTED - Intune will retry after reboot
}

# Detect SQL Server
$currentVersion = Get-CurrentSQLVersion -Instance $InstanceName

if (!$currentVersion) {
    Write-IntuneLog "SQL Server 2019 not found - skipping update" -Level "WARNING"
    Write-IntuneLog "This deployment is only applicable to devices with SQL Server 2019 installed"
    exit 0  # Success - not applicable
}

# Check if update is needed
if (!(Test-UpdateRequired -CurrentVersion $currentVersion.Version)) {
    Write-IntuneLog "SQL Server is already up to date: $($currentVersion.Version)" -Level "INFO"
    exit 0  # Success - already up to date
}

Write-IntuneLog "Update required: $($currentVersion.Version) -> 15.0.4430.1 (CU32)"

# Create temp directory
$tempDir = "C:\temp"
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    Write-IntuneLog "Created temp directory: $tempDir"
}

# Download update file
$downloadUrl = "https://download.microsoft.com/download/6/e/7/6e72dddf-dfa4-4889-bc3d-e5d3a0fd11ce/SQLServer2019-KB5054833-x64.exe"
$downloadPath = "$tempDir\SQLServer2019-KB5054833-x64.exe"

Write-IntuneLog "Downloading SQL Server 2019 CU32..."

try {
    if (Test-Path $downloadPath) {
        $fileInfo = Get-Item $downloadPath
        if ($fileInfo.Length -gt 1MB) {
            Write-IntuneLog "Update file already exists and appears valid"
        } else {
            Remove-Item $downloadPath -Force
            Write-IntuneLog "Removing incomplete download file"
        }
    }
    
    if (!(Test-Path $downloadPath)) {
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($downloadUrl, $downloadPath)
        $webClient.Dispose()
        
        $fileInfo = Get-Item $downloadPath
        Write-IntuneLog "Download completed: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
    }
    
} catch {
    Write-IntuneLog "Download failed: $($_.Exception.Message)" -Level "ERROR"
    exit 1603  # ERROR_INSTALL_FAILURE
}

# Stop SQL Server services
Write-IntuneLog "Stopping SQL Server services..."

try {
    $servicesToStop = @("SQLSERVERAGENT", "MSSQLSERVER", "SQLBrowser")
    $stoppedServices = @()
    
    foreach ($serviceName in $servicesToStop) {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq "Running") {
            Write-IntuneLog "Stopping service: $serviceName"
            Stop-Service -Name $serviceName -Force
            $stoppedServices += $serviceName
            
            # Wait for service to stop
            $timeout = 60
            while ($timeout -gt 0 -and (Get-Service -Name $serviceName).Status -eq "Running") {
                Start-Sleep -Seconds 2
                $timeout -= 2
            }
        }
    }
    
    Write-IntuneLog "Stopped services: $($stoppedServices -join ', ')"
    
} catch {
    Write-IntuneLog "Error stopping services: $($_.Exception.Message)" -Level "ERROR"
    exit 1603
}

# Install the update
Write-IntuneLog "Installing SQL Server 2019 CU32..."

try {
    $installArgs = @(
        "/quiet",
        "/IAcceptSQLServerLicenseTerms",
        "/Action=Patch",
        "/AllInstances",
        "/NORESTART"
    )
    
    Write-IntuneLog "Installation arguments: $($installArgs -join ' ')"
    
    $process = Start-Process -FilePath $downloadPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
    
    Write-IntuneLog "Installation completed with exit code: $($process.ExitCode)"
    
    if ($process.ExitCode -eq 0) {
        Write-IntuneLog "Installation successful"
    } elseif ($process.ExitCode -eq 3010) {
        Write-IntuneLog "Installation successful - Reboot required" -Level "WARNING"
    } else {
        Write-IntuneLog "Installation failed with exit code: $($process.ExitCode)" -Level "ERROR"
        throw "Installation failed"
    }
    
} catch {
    Write-IntuneLog "Installation error: $($_.Exception.Message)" -Level "ERROR"
    
    # Try to restart services
    foreach ($serviceName in $stoppedServices) {
        try {
            Start-Service -Name $serviceName -ErrorAction SilentlyContinue
        } catch {
            Write-IntuneLog "Failed to restart service $serviceName" -Level "WARNING"
        }
    }
    
    exit 1603
}

# Restart SQL Server services
Write-IntuneLog "Restarting SQL Server services..."

try {
    foreach ($serviceName in $stoppedServices) {
        Write-IntuneLog "Starting service: $serviceName"
        Start-Service -Name $serviceName
        
        # Wait for service to start
        $timeout = 120
        while ($timeout -gt 0 -and (Get-Service -Name $serviceName).Status -ne "Running") {
            Start-Sleep -Seconds 2
            $timeout -= 2
        }
        
        $service = Get-Service -Name $serviceName
        Write-IntuneLog "Service $serviceName status: $($service.Status)"
    }
    
} catch {
    Write-IntuneLog "Error restarting services: $($_.Exception.Message)" -Level "WARNING"
}

# Verify update
Write-IntuneLog "Verifying update installation..."

$updatedVersion = Get-CurrentSQLVersion -Instance $InstanceName
if ($updatedVersion) {
    Write-IntuneLog "Post-update version: $($updatedVersion.Version)"
    
    if ($updatedVersion.Version -eq "15.0.4430.1") {
        Write-IntuneLog "UPDATE SUCCESSFUL: SQL Server updated to CU32" -Level "INFO"
    } else {
        Write-IntuneLog "UPDATE VERIFICATION: Version changed from $($currentVersion.Version) to $($updatedVersion.Version)" -Level "INFO"
    }
} else {
    Write-IntuneLog "Could not verify updated version" -Level "WARNING"
}

# Cleanup
try {
    Remove-Item -Path $downloadPath -Force -ErrorAction SilentlyContinue
    Write-IntuneLog "Cleanup completed"
} catch {
    Write-IntuneLog "Cleanup warning: $($_.Exception.Message)" -Level "WARNING"
}

Write-IntuneLog "=== SQL Server 2019 Update - Intune Deployment Completed ==="
exit 0
