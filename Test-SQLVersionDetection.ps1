# Test SQL Server Version Detection
# This script tests the SQL Server version detection logic

Write-Host "Testing SQL Server 2019 Version Detection..." -ForegroundColor Green
Write-Host "=" * 50

# Function to get current SQL Server version (simplified version for testing)
function Get-CurrentSQLVersion {
    param([string]$Instance = "MSSQLSERVER")
    
    try {
        Write-Host "Detecting current SQL Server version for instance: $Instance" -ForegroundColor Cyan
        
        # Try registry method first (most reliable)
        # SQL Server 2019 uses MSSQL15.x format in registry
        $registryPaths = @()
        
        # Build registry paths for different instance configurations
        if ($Instance -eq "MSSQLSERVER") {
            $registryPaths += "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup"
        } else {
            $registryPaths += "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.$Instance\Setup"
        }
        
        # Also check common SQL Server 2019 registry locations
        $registryPaths += @(
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\Setup",
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQL2019\Setup",
            "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL15.SQLEXPRESS\Setup"
        )
        
        # Remove duplicates
        $registryPaths = $registryPaths | Select-Object -Unique
        
        Write-Host "Checking registry paths for SQL Server 2019 installation..." -ForegroundColor Yellow
        foreach ($regPath in $registryPaths) {
            Write-Host "  Checking: $regPath" -ForegroundColor Gray
            if (Test-Path $regPath) {
                Write-Host "    ✅ Path exists" -ForegroundColor Green
                $setupInfo = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
                if ($setupInfo -and $setupInfo.Version) {
                    Write-Host "    ✅ Found SQL Server version: $($setupInfo.Version)" -ForegroundColor Green
                    Write-Host "    ✅ Edition: $($setupInfo.Edition)" -ForegroundColor Green
                    Write-Host "    ✅ Patch Level: $($setupInfo.PatchLevel)" -ForegroundColor Green
                    return @{
                        Version = $setupInfo.Version
                        Edition = if ($setupInfo.Edition) { $setupInfo.Edition } else { "Unknown" }
                        PatchLevel = if ($setupInfo.PatchLevel) { $setupInfo.PatchLevel } else { "Unknown" }
                        ProductName = "SQL Server 2019"
                        DetectionMethod = "Registry ($regPath)"
                        RegistryPath = $regPath
                    }
                } else {
                    Write-Host "    ⚠️ Path exists but no version info found" -ForegroundColor Yellow
                }
            } else {
                Write-Host "    ❌ Path not found" -ForegroundColor Red
            }
        }
        
        Write-Host "Registry detection failed, trying SQLCMD method..." -ForegroundColor Yellow
        
        # Try SQLCMD method as fallback
        $sqlcmdPath = Get-ChildItem -Path "C:\Program Files\Microsoft SQL Server\*\Tools\Binn\SQLCMD.exe" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($sqlcmdPath) {
            Write-Host "Found SQLCMD at: $($sqlcmdPath.FullName)" -ForegroundColor Green
            $serverName = if ($Instance -eq "MSSQLSERVER") { $env:COMPUTERNAME } else { "$env:COMPUTERNAME\$Instance" }
            
            try {
                $versionQuery = "SELECT SERVERPROPERTY('ProductVersion') AS Version, SERVERPROPERTY('Edition') AS Edition, SERVERPROPERTY('ProductLevel') AS ProductLevel"
                $result = & $sqlcmdPath.FullName -S $serverName -E -Q $versionQuery -h -1 -W 2>$null
                
                if ($result -and $result.Count -gt 0) {
                    $versionLine = $result | Where-Object { $_ -match "^\d+\.\d+\.\d+\.\d+" } | Select-Object -First 1
                    if ($versionLine) {
                        Write-Host "Found SQL Server version via SQLCMD: $versionLine" -ForegroundColor Green
                        return @{
                            Version = $versionLine.Trim()
                            Edition = "Unknown"
                            PatchLevel = "Unknown"
                            ProductName = "SQL Server 2019"
                            DetectionMethod = "SQLCMD"
                        }
                    }
                }
            } catch {
                Write-Host "SQLCMD version detection failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "SQLCMD not found" -ForegroundColor Red
        }
        
        Write-Host "Could not detect SQL Server version" -ForegroundColor Red
        return $null
        
    } catch {
        Write-Host "Error detecting SQL Server version: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Test the detection
$result = Get-CurrentSQLVersion -Instance "MSSQLSERVER"

Write-Host "`n" + "=" * 50
Write-Host "DETECTION RESULTS:" -ForegroundColor Green
Write-Host "=" * 50

if ($result) {
    Write-Host "✅ SQL Server Detection: SUCCESS" -ForegroundColor Green
    Write-Host "   Version: $($result.Version)" -ForegroundColor White
    Write-Host "   Edition: $($result.Edition)" -ForegroundColor White
    Write-Host "   Product: $($result.ProductName)" -ForegroundColor White
    Write-Host "   Patch Level: $($result.PatchLevel)" -ForegroundColor White
    Write-Host "   Detection Method: $($result.DetectionMethod)" -ForegroundColor White
    if ($result.RegistryPath) {
        Write-Host "   Registry Path: $($result.RegistryPath)" -ForegroundColor White
    }
    
    # Determine what update is needed
    $currentVersion = [Version]$result.Version
    $latestVersion = [Version]"15.0.4430.1"  # CU32
    
    Write-Host "`nUPDATE STATUS:" -ForegroundColor Yellow
    if ($currentVersion -lt $latestVersion) {
        Write-Host "   🔄 UPDATE NEEDED" -ForegroundColor Yellow
        Write-Host "   Current: $($result.Version)" -ForegroundColor White
        Write-Host "   Latest:  15.0.4430.1 (CU32)" -ForegroundColor White
    } else {
        Write-Host "   ✅ UP TO DATE" -ForegroundColor Green
        Write-Host "   Current: $($result.Version)" -ForegroundColor White
    }
} else {
    Write-Host "❌ SQL Server Detection: FAILED" -ForegroundColor Red
    Write-Host "   No SQL Server 2019 installation found" -ForegroundColor White
    Write-Host "   Please verify SQL Server 2019 is installed" -ForegroundColor White
}

Write-Host "`n" + "=" * 50
Write-Host "Test completed!" -ForegroundColor Green
